/* Modern Admin Dashboard Styles */

:root {
    --admin-primary: #4f46e5;
    --admin-primary-dark: #3730a3;
    --admin-secondary: #10b981;
    --admin-danger: #ef4444;
    --admin-warning: #f59e0b;
    --admin-info: #06b6d4;
    --admin-success: #10b981;
    --admin-bg: #f8fafc;
    --admin-card-bg: #ffffff;
    --admin-sidebar-bg: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    --admin-text: #1e293b;
    --admin-text-muted: #64748b;
    --admin-border: #e2e8f0;
    --admin-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --admin-radius: 12px;
    --admin-radius-lg: 16px;
    --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    --gradient-success: linear-gradient(135deg, #10b981, #059669);
    --gradient-warning: linear-gradient(135deg, #f59e0b, #d97706);
    --gradient-danger: linear-gradient(135deg, #ef4444, #dc2626);
    --gradient-info: linear-gradient(135deg, #06b6d4, #0891b2);
}

body {
    overflow-x: hidden;
    background: var(--admin-bg);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--admin-text);
    line-height: 1.6;
}

#wrapper {
    display: flex;
    min-height: 100vh;
}

/* Enhanced Modern Sidebar */
.modern-sidebar {
    min-height: 100vh;
    width: 300px;
    margin-left: -300px;
    transition: var(--admin-transition);
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

.modern-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    pointer-events: none;
}

/* Sidebar Header */
.sidebar-header {
    padding: 1rem 1rem 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    transition: var(--admin-transition);
}

.sidebar-brand:hover {
    color: white;
    transform: scale(1.02);
}

.brand-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
    transition: var(--admin-transition);
}

.brand-icon:hover {
    transform: rotate(5deg) scale(1.1);
    box-shadow: 0 12px 24px rgba(79, 70, 229, 0.4);
}

.brand-logo {
    max-height: 20px;
    filter: brightness(0) invert(1);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.1rem;
    font-weight: 700;
    line-height: 1.1;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0;
}

.brand-subtitle {
    font-size: 0.7rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 0.25rem 0;
    flex: 1;
}

.nav-section {
    margin-bottom: 0.5rem;
}

.nav-section-title {
    font-size: 0.7rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.5);
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 0 1rem 0.25rem;
    margin-bottom: 0.25rem;
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2), transparent);
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    margin: 0.05rem 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: var(--admin-transition);
}

.nav-item:hover {
    color: white;
    transform: translateX(8px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-item:hover::before {
    opacity: 1;
}

.nav-item:hover .nav-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    box-shadow: 0 6px 12px rgba(79, 70, 229, 0.3);
}

.nav-item:hover .nav-indicator {
    width: 4px;
    background: linear-gradient(180deg, var(--admin-primary), var(--admin-secondary));
}

.nav-item.active {
    color: white;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(16, 185, 129, 0.1));
    border: 1px solid rgba(79, 70, 229, 0.3);
    box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
    transform: translateX(4px);
}

.nav-item.active .nav-icon {
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    box-shadow: 0 6px 12px rgba(79, 70, 229, 0.4);
    transform: scale(1.05);
}

.nav-item.active .nav-indicator {
    width: 4px;
    background: linear-gradient(180deg, var(--admin-primary), var(--admin-secondary));
    box-shadow: 0 0 8px rgba(79, 70, 229, 0.6);
}

.nav-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    margin-right: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
}

.nav-text {
    flex: 1;
    font-size: 0.85rem;
    font-weight: 500;
}

.nav-indicator {
    width: 0;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 2px 0 0 2px;
    transition: var(--admin-transition);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 0.75rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
}

.sidebar-user {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: var(--admin-transition);
}

.sidebar-user:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 1rem;
    color: white;
    box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

.user-info {
    flex: 1;
}

.user-name {
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
    line-height: 1.1;
}

.user-role {
    font-size: 0.65rem;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 500;
}

/* Sidebar Responsive */
#sidebar-wrapper {
    width: 300px;
    margin-left: -300px;
}

#sidebar-wrapper .list-group {
    width: 300px;
}

#page-content-wrapper {
    min-width: 100vw;
    background: var(--admin-bg);
}

#wrapper.toggled #sidebar-wrapper {
    margin-left: 0;
}

/* Modern Top Navigation Bar */
.modern-topbar {
    background: var(--admin-card-bg);
    border-bottom: 1px solid var(--admin-border);
    box-shadow: var(--admin-shadow);
    position: sticky;
    top: 0;
    z-index: 999;
    backdrop-filter: blur(10px);
}

.topbar-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1.5rem;
    max-width: 100%;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle-btn {
    background: none;
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
    padding: 0.5rem;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.sidebar-toggle-btn:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
    transform: scale(1.05);
}

.global-search {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    width: 400px;
    max-width: 100%;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    background: var(--admin-bg);
    color: var(--admin-text);
    font-size: 0.875rem;
    transition: var(--admin-transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: white;
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--admin-text-muted);
    font-size: 0.875rem;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.quick-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-action-btn {
    background: none;
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
    padding: 0.5rem;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    text-decoration: none;
}

.quick-action-btn:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
    transform: translateY(-1px);
}

/* Notifications Dropdown */
.notifications-dropdown {
    position: relative;
}

.notification-btn {
    background: none;
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
    padding: 0.5rem;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    position: relative;
}

.notification-btn:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--admin-danger);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 50px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

.notification-menu {
    width: 320px;
    border: none;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow-xl);
    padding: 0;
    margin-top: 0.5rem;
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--admin-text);
}

.mark-all-read {
    font-size: 0.8rem;
    color: var(--admin-primary);
    cursor: pointer;
    transition: var(--admin-transition);
}

.mark-all-read:hover {
    color: var(--admin-primary-dark);
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--admin-border);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    transition: var(--admin-transition);
}

.notification-item:hover {
    background: var(--admin-bg);
}

.notification-item.unread {
    background: rgba(79, 70, 229, 0.05);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
}

.notification-text {
    margin: 0;
    font-size: 0.875rem;
    color: var(--admin-text);
    line-height: 1.4;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
}

.notification-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--admin-border);
    text-align: center;
}

.view-all-notifications {
    color: var(--admin-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--admin-transition);
}

.view-all-notifications:hover {
    color: var(--admin-primary-dark);
}

/* User Profile Dropdown */
.user-profile-dropdown {
    position: relative;
}

.user-profile-btn {
    background: none;
    border: 1px solid var(--admin-border);
    padding: 0.5rem;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--admin-text);
}

.user-profile-btn:hover {
    background: var(--admin-bg);
    border-color: var(--admin-primary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.1;
    margin: 0;
}

.user-role {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    line-height: 1.1;
    margin: 0;
}

.user-menu {
    width: 280px;
    border: none;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow-xl);
    padding: 0;
    margin-top: 0.5rem;
}

.user-menu-header {
    padding: 1.5rem;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar-large {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-avatar-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details h6 {
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
}

.user-details p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
}

.user-menu-items {
    padding: 0.5rem 0;
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--admin-text);
    text-decoration: none;
    transition: var(--admin-transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 0.875rem;
}

.user-menu-item:hover {
    background: var(--admin-bg);
    color: var(--admin-primary);
}

.user-menu-item i {
    width: 16px;
    text-align: center;
}

.user-menu-divider {
    height: 1px;
    background: var(--admin-border);
    margin: 0.5rem 0;
}

.logout-btn {
    color: var(--admin-danger) !important;
}

.logout-btn:hover {
    background: rgba(239, 68, 68, 0.1) !important;
    color: var(--admin-danger) !important;
}

/* Main Content */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

/* Legacy Navigation Items - Hidden */
.list-group-item {
    display: none;
}

/* Dashboard Container */
.dashboard-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
}

/* Dashboard Header */
.dashboard-header {
    margin-bottom: 2rem;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.header-text {
    flex: 1;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-text);
    margin: 0;
    display: flex;
    align-items: center;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dashboard-subtitle {
    font-size: 1rem;
    color: var(--admin-text-muted);
    margin: 0.5rem 0 0;
    line-height: 1.5;
}

.current-time {
    font-weight: 500;
    color: var(--admin-primary);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: var(--admin-radius);
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--admin-transition);
    border: none;
    text-decoration: none;
    cursor: pointer;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--admin-shadow);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--admin-shadow-lg);
}

.action-btn.secondary {
    background: white;
    color: var(--admin-text);
    border: 1px solid var(--admin-border);
}

.action-btn.secondary:hover {
    background: var(--admin-bg);
    border-color: var(--admin-primary);
    color: var(--admin-primary);
}

/* KPI Grid */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.kpi-card {
    background: var(--admin-card-bg);
    border-radius: var(--admin-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--admin-shadow-xl);
}

.kpi-card.revenue-card::before {
    background: var(--gradient-success);
}

.kpi-card.projects-card::before {
    background: var(--gradient-primary);
}

.kpi-card.clients-card::before {
    background: var(--gradient-info);
}

.kpi-card.notifications-card::before {
    background: var(--gradient-warning);
}

.kpi-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.kpi-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--admin-radius);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    box-shadow: var(--admin-shadow);
}

.kpi-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
}

.kpi-trend.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
}

.kpi-trend.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-danger);
}

.kpi-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    background: rgba(79, 70, 229, 0.1);
    color: var(--admin-primary);
}

.kpi-badge.urgent {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-danger);
}

.kpi-content {
    margin-bottom: 1rem;
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--admin-text);
    margin: 0 0 0.25rem;
    line-height: 1;
}

.kpi-label {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    margin: 0;
    font-weight: 500;
}

.kpi-breakdown {
    display: flex;
    gap: 1rem;
    margin-top: 0.75rem;
}

.breakdown-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.breakdown-label {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    font-weight: 500;
}

.breakdown-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text);
}

.kpi-chart {
    height: 40px;
    margin-top: 1rem;
}

/* Progress Ring */
.progress-ring {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.progress-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: conic-gradient(var(--admin-primary) var(--progress, 0%), var(--admin-border) 0%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--admin-card-bg);
    position: absolute;
}

.progress-text {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text);
    position: relative;
    z-index: 1;
}

.progress-label {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin: 0;
}

/* Client Stats */
.client-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: var(--admin-text-muted);
}

.stat-item i {
    color: var(--admin-primary);
}

/* Notification List */
.notification-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 0.75rem;
}

.kpi-actions {
    display: flex;
    gap: 0.5rem;
}

.kpi-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(79, 70, 229, 0.1);
    border: none;
    color: var(--admin-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--admin-transition);
    cursor: pointer;
}

.kpi-action-btn:hover {
    background: var(--admin-primary);
    color: white;
    transform: scale(1.1);
}

/* Clickable KPI Cards */
.kpi-card-link {
    text-decoration: none;
    color: inherit;
    display: block;
    position: relative;
}

.kpi-card-link:hover {
    text-decoration: none;
    color: inherit;
}

.kpi-card-link .kpi-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.kpi-card-link .kpi-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(79, 70, 229, 0.05);
    opacity: 0;
    transition: var(--admin-transition);
    pointer-events: none;
}

.kpi-card-link:hover .kpi-card::after {
    opacity: 1;
}

.kpi-card-link:hover .kpi-card {
    transform: translateY(-6px);
    box-shadow: var(--admin-shadow-xl);
}

.kpi-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(79, 70, 229, 0.95);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--admin-transition);
    font-weight: 600;
    font-size: 0.875rem;
    backdrop-filter: blur(4px);
}

.kpi-card-link:hover .kpi-overlay {
    opacity: 1;
}

.kpi-overlay i {
    font-size: 1.5rem;
    margin-bottom: 0.25rem;
}

/* Quick Stats Links */
.quick-stat-link {
    text-decoration: none;
    color: inherit;
    display: block;
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.quick-stat-link:hover {
    text-decoration: none;
    color: inherit;
    transform: scale(1.02);
}

.quick-stat-link .quick-stat-item {
    transition: var(--admin-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.quick-stat-link .quick-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--admin-transition);
    border-radius: var(--admin-radius);
}

.quick-stat-link:hover .quick-stat-item::before {
    opacity: 0.1;
}

.quick-stat-link:hover .quick-stat-item {
    background: rgba(79, 70, 229, 0.05);
    transform: scale(1.05);
    box-shadow: var(--admin-shadow);
}

.quick-stat-link .quick-stat-item .stat-icon {
    transition: var(--admin-transition);
    position: relative;
    z-index: 1;
}

.quick-stat-link .quick-stat-item .stat-content {
    position: relative;
    z-index: 1;
}

.quick-stat-link:hover .quick-stat-item .stat-icon {
    transform: scale(1.1);
    box-shadow: var(--admin-shadow-lg);
}

/* Hover effects for data table rows */
.table-row {
    cursor: pointer;
    position: relative;
}

.table-row::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--admin-primary);
    opacity: 0;
    transition: var(--admin-transition);
    border-radius: 0 4px 4px 0;
}

.table-row:hover::before {
    opacity: 1;
}

.table-row:hover {
    background: rgba(79, 70, 229, 0.05);
    transform: translateX(8px);
    box-shadow: var(--admin-shadow);
}

/* Activity item hover effects */
.activity-item {
    cursor: pointer;
    position: relative;
}

.activity-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--admin-primary);
    opacity: 0;
    transition: var(--admin-transition);
    border-radius: 0 4px 4px 0;
}

.activity-item:hover::before {
    opacity: 1;
}

.activity-item:hover {
    background: rgba(79, 70, 229, 0.05);
    transform: translateX(8px);
    box-shadow: var(--admin-shadow);
}

/* Chart card hover effects */
.analytics-card {
    cursor: pointer;
    position: relative;
}

.analytics-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--admin-transition);
    border-radius: var(--admin-radius-lg) var(--admin-radius-lg) 0 0;
}

.analytics-card:hover::before {
    opacity: 1;
}

/* Loading states */
.kpi-card-link.loading .kpi-card {
    opacity: 0.7;
    pointer-events: none;
}

.kpi-card-link.loading .kpi-overlay {
    opacity: 1;
    background: rgba(0, 0, 0, 0.8);
}

.kpi-card-link.loading .kpi-overlay::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus states for accessibility */
.kpi-card-link:focus,
.quick-stat-link:focus {
    outline: 2px solid var(--admin-primary);
    outline-offset: 2px;
    border-radius: var(--admin-radius);
}

.kpi-card-link:focus .kpi-card,
.quick-stat-link:focus .quick-stat-item {
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: var(--admin-card-bg);
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
    overflow: hidden;
}

.analytics-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.analytics-card .card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-content {
    flex: 1;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
    display: flex;
    align-items: center;
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    margin: 0.25rem 0 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-filter {
    display: flex;
    background: var(--admin-bg);
    border-radius: var(--admin-radius);
    padding: 0.25rem;
    border: 1px solid var(--admin-border);
}

.filter-btn {
    padding: 0.375rem 0.75rem;
    border: none;
    background: none;
    color: var(--admin-text-muted);
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: calc(var(--admin-radius) - 2px);
    transition: var(--admin-transition);
    cursor: pointer;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--admin-primary);
    color: white;
}

.refresh-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: none;
    border: 1px solid var(--admin-border);
    color: var(--admin-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--admin-transition);
    cursor: pointer;
}

.refresh-btn:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
    transform: rotate(180deg);
}

.analytics-card .card-body {
    padding: 1.5rem;
}

/* Chart Containers */
.chart-container {
    height: 300px;
    position: relative;
}

.donut-chart-container {
    height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

.legend-label {
    color: var(--admin-text);
    font-weight: 500;
}

/* Activity Timeline */
.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: var(--admin-bg);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.activity-item:hover {
    background: rgba(79, 70, 229, 0.05);
    transform: translateX(4px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-icon.project {
    background: var(--gradient-primary);
}

.activity-icon.client {
    background: var(--gradient-success);
}

.activity-icon.invoice {
    background: var(--gradient-info);
}

.activity-content {
    flex: 1;
}

.activity-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.activity-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
}

.activity-time {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
}

.activity-description {
    font-size: 0.8rem;
    color: var(--admin-text-muted);
    margin: 0;
    line-height: 1.4;
}

.activity-footer {
    padding: 1rem;
    border-top: 1px solid var(--admin-border);
    text-align: center;
}

.view-all-link {
    color: var(--admin-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--admin-transition);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.view-all-link:hover {
    color: var(--admin-primary-dark);
    transform: translateX(2px);
}

/* Quick Stats Grid */
.quick-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.quick-stat-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--admin-bg);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.quick-stat-item:hover {
    background: rgba(79, 70, 229, 0.05);
    transform: scale(1.02);
}

.quick-stat-item .stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--admin-radius);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.quick-stat-item .stat-content {
    flex: 1;
}

.quick-stat-item .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--admin-text);
    margin: 0;
    line-height: 1;
}

.quick-stat-item .stat-label {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin: 0.25rem 0 0;
    font-weight: 500;
}

/* Data Tables Grid */
.data-tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.data-table-card {
    background: var(--admin-card-bg);
    border-radius: var(--admin-radius-lg);
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
    overflow: hidden;
}

.data-table-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--admin-border);
    background: var(--admin-bg);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text);
    margin: 0;
    display: flex;
    align-items: center;
}

.table-subtitle {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    margin: 0.25rem 0 0;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--admin-primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--admin-transition);
    padding: 0.5rem 1rem;
    border-radius: var(--admin-radius);
    border: 1px solid transparent;
}

.view-all-btn:hover {
    background: rgba(79, 70, 229, 0.1);
    border-color: var(--admin-primary);
    transform: translateX(2px);
}

.table-container {
    padding: 1rem;
}

.modern-table {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.table-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--admin-bg);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.table-row:hover {
    background: rgba(79, 70, 229, 0.05);
    transform: translateX(4px);
}

.row-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.row-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--admin-radius);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.row-icon.invoice {
    background: var(--gradient-info);
}

.row-details {
    flex: 1;
}

.row-title {
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.row-title a {
    color: var(--admin-text);
    text-decoration: none;
    transition: var(--admin-transition);
}

.row-title a:hover {
    color: var(--admin-primary);
}

.row-subtitle {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    margin: 0.25rem 0 0;
}

.row-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meta-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    background: var(--admin-bg);
    color: var(--admin-text-muted);
    border: 1px solid var(--admin-border);
}

.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.paid {
    background: rgba(16, 185, 129, 0.1);
    color: var(--admin-success);
}

.status-badge.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--admin-warning);
}

.status-badge.overdue {
    background: rgba(239, 68, 68, 0.1);
    color: var(--admin-danger);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 992px) {
    .main-content {
        padding: 1.5rem;
    }

    .dashboard-title {
        font-size: 1.75rem;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .topbar-container {
        padding: 0.5rem 1rem;
    }

    .search-input-wrapper {
        width: 300px;
    }

    .user-info {
        display: none;
    }

    .data-tables-grid {
        grid-template-columns: 1fr;
    }

    .quick-stats-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .kpi-card {
        padding: 1rem;
    }

    .kpi-value {
        font-size: 2rem;
    }

    .topbar-container {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .topbar-left {
        flex: 1;
        min-width: 0;
    }

    .search-input-wrapper {
        width: 100%;
        max-width: 250px;
    }

    .topbar-right {
        gap: 0.5rem;
    }

    .analytics-card .card-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .analytics-card .card-body {
        padding: 1rem;
    }

    .chart-container {
        height: 250px;
    }

    .table-header {
        padding: 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .table-container {
        padding: 0.5rem;
    }

    .table-row {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .row-content {
        width: 100%;
    }

    .row-meta {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 0.75rem;
    }

    .dashboard-header {
        margin-bottom: 1.5rem;
    }

    .dashboard-title {
        font-size: 1.25rem;
    }

    .dashboard-subtitle {
        font-size: 0.875rem;
    }

    .action-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .kpi-card {
        padding: 0.75rem;
    }

    .kpi-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .kpi-value {
        font-size: 1.75rem;
    }

    .topbar-container {
        padding: 0.5rem;
    }

    .search-input-wrapper {
        max-width: 200px;
    }

    .search-input {
        padding: 0.5rem 0.75rem 0.5rem 2rem;
        font-size: 0.8rem;
    }

    .notification-menu,
    .user-menu {
        width: 280px;
        max-width: calc(100vw - 2rem);
    }

    .analytics-grid {
        gap: 1rem;
    }

    .data-tables-grid {
        gap: 1rem;
    }

    .activity-timeline {
        max-height: 300px;
    }

    .activity-item {
        padding: 0.75rem;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --admin-bg: #0f172a;
        --admin-card-bg: #1e293b;
        --admin-text: #f1f5f9;
        --admin-text-muted: #94a3b8;
        --admin-border: #334155;
    }
}

/* Print styles */
@media print {
    .modern-topbar,
    .sidebar-wrapper,
    .header-actions,
    .kpi-actions,
    .header-actions {
        display: none !important;
    }

    .main-content {
        padding: 0;
    }

    .kpi-card,
    .analytics-card,
    .data-table-card {
        box-shadow: none;
        border: 1px solid var(--admin-border);
        break-inside: avoid;
    }
}

/* Animation keyframes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility classes */
.animate-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-slide-in {
    animation: slideInRight 0.4s ease-out;
}

/* Focus styles for accessibility */
.action-btn:focus,
.quick-action-btn:focus,
.notification-btn:focus,
.user-profile-btn:focus,
.search-input:focus {
    outline: 2px solid var(--admin-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --admin-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        --admin-shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.3);
        --admin-shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.3);
    }

    .kpi-card,
    .analytics-card,
    .data-table-card {
        border: 2px solid var(--admin-border);
    }
}

/* Modern Admin Cards */
.admin-card {
    background: var(--admin-card-bg);
    border: 1px solid var(--admin-border);
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
    overflow: hidden;
}

.admin-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.admin-stat-card {
    background: var(--admin-card-bg);
    border: none;
    border-radius: var(--admin-radius);
    box-shadow: var(--admin-shadow);
    transition: var(--admin-transition);
    position: relative;
    overflow: hidden;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
}

.admin-stat-card.success::before {
    background: var(--admin-success);
}

.admin-stat-card.info::before {
    background: var(--admin-info);
}

.admin-stat-card.warning::before {
    background: var(--admin-warning);
}

.admin-stat-card.danger::before {
    background: var(--admin-danger);
}

.admin-stat-card:hover {
    box-shadow: var(--admin-shadow-lg);
    transform: translateY(-2px);
}

.admin-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: var(--gradient-primary);
}

.admin-stat-icon.success {
    background: var(--admin-success);
}

.admin-stat-icon.info {
    background: var(--admin-info);
}

.admin-stat-icon.warning {
    background: var(--admin-warning);
}

.admin-stat-icon.danger {
    background: var(--admin-danger);
}

.admin-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-text);
    margin: 0;
}

.admin-stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--admin-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

/* Modern Navbar */
.navbar {
    background: var(--admin-card-bg) !important;
    border-bottom: 1px solid var(--admin-border);
    box-shadow: var(--admin-shadow);
}

.navbar .btn {
    background: transparent;
    border: 1px solid var(--admin-border);
    color: var(--admin-text);
    border-radius: var(--admin-radius);
    transition: var(--admin-transition);
}

.navbar .btn:hover {
    background: var(--admin-primary);
    color: white;
    border-color: var(--admin-primary);
}

/* Modern Tables */
.table {
    background: var(--admin-card-bg);
    border-radius: var(--admin-radius);
    overflow: hidden;
    box-shadow: var(--admin-shadow);
}

.table th {
    background: var(--admin-bg);
    border-bottom: 2px solid var(--admin-border);
    font-weight: 600;
    color: var(--admin-text);
    padding: 1rem;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--admin-border);
    color: var(--admin-text);
}

.table tbody tr:hover {
    background: rgba(79, 70, 229, 0.05);
}

/* Modern Buttons */
.btn-modern-admin {
    padding: 0.75rem 1.5rem;
    border-radius: var(--admin-radius);
    font-weight: 600;
    transition: var(--admin-transition);
    border: none;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-modern-admin.primary {
    background: var(--admin-primary);
    color: white;
}

.btn-modern-admin.primary:hover {
    background: var(--admin-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-lg);
}

.btn-modern-admin.secondary {
    background: var(--admin-secondary);
    color: white;
}

.btn-modern-admin.secondary:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--admin-shadow-lg);
}

/* Legacy support */
.border-left-primary {
    border-left: 0.25rem solid var(--admin-primary) !important;
}

.border-left-success {
    border-left: 0.25rem solid var(--admin-success) !important;
}

.border-left-info {
    border-left: 0.25rem solid var(--admin-info) !important;
}

.border-left-warning {
    border-left: 0.25rem solid var(--admin-warning) !important;
}

.border-left-danger {
    border-left: 0.25rem solid var(--admin-danger) !important;
}

.text-gray-300 {
    color: var(--admin-text-muted) !important;
}

.text-gray-800 {
    color: var(--admin-text) !important;
}

/* Responsive Design */
@media (min-width: 768px) {
    #sidebar-wrapper {
        margin-left: 0;
    }

    #page-content-wrapper {
        min-width: 0;
        width: 100%;
    }

    #wrapper.toggled #sidebar-wrapper {
        margin-left: -300px;
    }
}

/* Sidebar Animation Enhancements */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.nav-item {
    animation: fadeInUp 0.3s ease-out;
}

.nav-item:nth-child(1) { animation-delay: 0.1s; }
.nav-item:nth-child(2) { animation-delay: 0.2s; }
.nav-item:nth-child(3) { animation-delay: 0.3s; }
.nav-item:nth-child(4) { animation-delay: 0.4s; }
.nav-item:nth-child(5) { animation-delay: 0.5s; }

.modern-sidebar {
    animation: slideInLeft 0.5s ease-out;
}

/* Enhanced Hover Effects */
.nav-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    pointer-events: none;
}

.nav-item:hover .nav-icon::after {
    width: 40px;
    height: 40px;
}

/* Pulse Animation for Active Items */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
    }
}

.nav-item.active .nav-icon {
    animation: pulse 2s infinite;
}

@media (max-width: 767px) {
    .admin-stat-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        border-radius: var(--admin-radius);
    }
}
