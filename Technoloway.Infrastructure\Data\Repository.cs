using System.Linq.Expressions;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Common;
using Technoloway.Core.Interfaces;

namespace Technoloway.Infrastructure.Data;

public class Repository<T> : IRepository<T> where T : BaseEntity
{
    protected readonly ApplicationDbContext _context;

    public Repository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<T?> GetByIdAsync(int id)
    {
        return await _context.Set<T>().FindAsync(id);
    }

    public async Task<IReadOnlyList<T>> ListAllAsync()
    {
        return await _context.Set<T>().Where(e => !e.IsDeleted).ToListAsync();
    }

    public async Task<IReadOnlyList<T>> ListAsync(Expression<Func<T, bool>> predicate)
    {
        return await _context.Set<T>().Where(predicate).Where(e => !e.IsDeleted).ToListAsync();
    }

    public async Task<T> AddAsync(T entity)
    {
        _context.Set<T>().Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }

    public async Task UpdateAsync(T entity)
    {
        entity.UpdatedAt = DateTime.UtcNow;

        try
        {
            // Check if entity is already being tracked
            var existingEntry = _context.Entry(entity);
            if (existingEntry.State == EntityState.Detached)
            {
                // Entity is not tracked, so attach and mark as modified
                _context.Set<T>().Attach(entity);
                existingEntry.State = EntityState.Modified;
            }
            else
            {
                // Entity is already tracked, just mark as modified
                existingEntry.State = EntityState.Modified;
            }

            await _context.SaveChangesAsync();
        }
        catch (DbUpdateConcurrencyException)
        {
            // If there's a concurrency issue, try using Update method instead
            _context.Set<T>().Update(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task UpdateDetachedAsync(T entity)
    {
        entity.UpdatedAt = DateTime.UtcNow;

        // For detached entities, use Update which will handle tracking automatically
        _context.Set<T>().Update(entity);
        await _context.SaveChangesAsync();
    }

    public async Task DeleteAsync(T entity)
    {
        entity.IsDeleted = true;
        entity.UpdatedAt = DateTime.UtcNow;

        // Ensure the entity is being tracked by EF
        var existingEntry = _context.Entry(entity);
        if (existingEntry.State == EntityState.Detached)
        {
            _context.Set<T>().Attach(entity);
            existingEntry.State = EntityState.Modified;
        }
        else
        {
            existingEntry.State = EntityState.Modified;
        }

        await _context.SaveChangesAsync();
    }

    public async Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null)
    {
        var query = _context.Set<T>().Where(e => !e.IsDeleted);

        if (predicate != null)
        {
            query = query.Where(predicate);
        }

        return await query.CountAsync();
    }

    // Extension method for IQueryable access
    public IQueryable<T> GetAll()
    {
        return _context.Set<T>().AsQueryable();
    }

    // Extension method for IQueryable access without tracking
    public IQueryable<T> GetAllAsNoTracking()
    {
        return _context.Set<T>().AsNoTracking();
    }
}
