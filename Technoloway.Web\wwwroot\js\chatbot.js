// TechnoloWay Chatbot Logic
class TechnoloWayChatbot {
    constructor() {
        this.isOpen = false;
        this.isMinimized = false;
        this.conversationState = 'welcome';
        this.userContext = {};
        this.messageHistory = [];
        
        this.init();
    }

    init() {
        this.createChatbotHTML();
        this.bindEvents();
        this.setupAutoGreeting();
    }

    createChatbotHTML() {
        const chatbotHTML = `
            <!-- Chatbot Toggle Button -->
            <div id="chatbot-toggle" class="chatbot-toggle">
                <i class="fas fa-comments"></i>
                <span class="notification-badge" id="notification-badge">1</span>
            </div>

            <!-- Chatbot Container -->
            <div id="chatbot-container" class="chatbot-container hidden">
                <!-- Header -->
                <div class="chatbot-header">
                    <div class="header-content">
                        <div class="bot-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="bot-info">
                            <h3>Alex</h3>
                            <span class="status online">
                                <i class="fas fa-circle"></i>
                                Online
                            </span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button id="minimize-btn" class="action-btn" title="Minimize">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button id="close-btn" class="action-btn" title="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Messages Area -->
                <div id="messages-container" class="messages-container">
                    <div class="welcome-message">
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-bubble">
                                    <p>👋 Hi there! Welcome to TechnoloWay!</p>
                                    <p>I'm Alex, your virtual assistant. I'm here to help you learn about our software development services.</p>
                                    <p>What brings you here today?</p>
                                </div>
                                <div class="message-time">Just now</div>
                            </div>
                        </div>
                        
                        <!-- Quick Action Buttons -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" data-action="services">
                                <i class="fas fa-code"></i>
                                Our Services
                            </button>
                            <button class="quick-action-btn" data-action="quote">
                                <i class="fas fa-calculator"></i>
                                Get Quote
                            </button>
                            <button class="quick-action-btn" data-action="portfolio">
                                <i class="fas fa-briefcase"></i>
                                Portfolio
                            </button>
                            <button class="quick-action-btn" data-action="human">
                                <i class="fas fa-user"></i>
                                Talk to Human
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div id="typing-indicator" class="typing-indicator hidden">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-container">
                        <input 
                            type="text" 
                            id="message-input" 
                            placeholder="Type your message..."
                            autocomplete="off"
                        >
                        <button id="send-btn" class="send-btn" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- Suggested Responses -->
                    <div id="suggested-responses" class="suggested-responses hidden">
                        <!-- Dynamic suggestions will be inserted here -->
                    </div>
                </div>

                <!-- Footer -->
                <div class="chatbot-footer">
                    <span>Powered by TechnoloWay AI</span>
                </div>
            </div>
        `;

        // Append to body
        document.body.insertAdjacentHTML('beforeend', chatbotHTML);
    }

    bindEvents() {
        // Toggle chatbot
        document.getElementById('chatbot-toggle').addEventListener('click', () => {
            this.toggleChatbot();
        });

        // Header actions
        document.getElementById('minimize-btn').addEventListener('click', () => {
            this.minimizeChatbot();
        });

        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeChatbot();
        });

        // Message input
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');

        messageInput.addEventListener('input', (e) => {
            sendBtn.disabled = !e.target.value.trim();
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !sendBtn.disabled) {
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Quick actions
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    toggleChatbot() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');
        const badge = document.getElementById('notification-badge');

        if (this.isOpen) {
            this.closeChatbot();
        } else {
            container.classList.remove('hidden');
            container.classList.add('fade-in');
            toggle.style.display = 'none';
            badge.style.display = 'none';
            this.isOpen = true;
            
            // Focus input
            setTimeout(() => {
                document.getElementById('message-input').focus();
            }, 300);
        }
    }

    closeChatbot() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');

        container.classList.add('hidden');
        toggle.style.display = 'flex';
        this.isOpen = false;
        this.isMinimized = false;
    }

    minimizeChatbot() {
        const container = document.getElementById('chatbot-container');
        this.isMinimized = !this.isMinimized;
        
        if (this.isMinimized) {
            container.classList.add('minimized');
        } else {
            container.classList.remove('minimized');
        }
    }

    sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        input.value = '';
        document.getElementById('send-btn').disabled = true;

        // Show typing indicator
        this.showTypingIndicator();

        // Process message
        setTimeout(() => {
            this.processUserMessage(message);
        }, 1000 + Math.random() * 1000); // Simulate processing time
    }

    addMessage(content, sender, options = {}) {
        const messagesContainer = document.getElementById('messages-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message slide-up`;

        const avatar = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
        const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-bubble">
                    ${typeof content === 'string' ? `<p>${content}</p>` : content}
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);

        // Add quick actions if provided
        if (options.quickActions) {
            this.addQuickActions(options.quickActions);
        }

        // Add suggestions if provided
        if (options.suggestions) {
            this.showSuggestions(options.suggestions);
        }

        this.scrollToBottom();
        this.messageHistory.push({ content, sender, timestamp: new Date() });
    }

    addQuickActions(actions) {
        const messagesContainer = document.getElementById('messages-container');
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'quick-actions slide-up';

        actions.forEach(action => {
            const btn = document.createElement('button');
            btn.className = 'quick-action-btn';
            btn.dataset.action = action.value;
            btn.innerHTML = `<i class="${action.icon}"></i> ${action.label}`;
            btn.addEventListener('click', () => {
                this.handleQuickAction(action.value);
            });
            actionsDiv.appendChild(btn);
        });

        messagesContainer.appendChild(actionsDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        document.getElementById('typing-indicator').classList.remove('hidden');
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        document.getElementById('typing-indicator').classList.add('hidden');
    }

    scrollToBottom() {
        const container = document.getElementById('messages-container');
        container.scrollTop = container.scrollHeight;
    }

    async processUserMessage(message) {
        this.hideTypingIndicator();

        try {
            // Call the API to get response with real database data
            const response = await fetch('/api/chatbot/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    context: this.userContext
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.addMessage(data.content, 'bot', {
                    quickActions: data.quickActions,
                    suggestions: data.suggestions
                });
            } else {
                // Fallback to local response if API fails
                const fallbackResponse = this.generateLocalResponse(message);
                this.addMessage(fallbackResponse.content, 'bot', fallbackResponse.options);
            }
        } catch (error) {
            console.error('API Error:', error);
            // Fallback to local response
            const fallbackResponse = this.generateLocalResponse(message);
            this.addMessage(fallbackResponse.content, 'bot', fallbackResponse.options);
        }
    }

    generateLocalResponse(message) {
        const lowerMessage = message.toLowerCase();

        // Intent detection (fallback)
        if (this.containsKeywords(lowerMessage, ['service', 'services', 'what do you do', 'offerings'])) {
            return this.getServicesResponse();
        }

        if (this.containsKeywords(lowerMessage, ['quote', 'price', 'cost', 'budget', 'estimate'])) {
            return this.getQuoteResponse();
        }

        if (this.containsKeywords(lowerMessage, ['portfolio', 'work', 'projects', 'examples'])) {
            return this.getPortfolioResponse();
        }

        if (this.containsKeywords(lowerMessage, ['human', 'person', 'agent', 'talk', 'speak'])) {
            return this.getHumanHandoffResponse();
        }

        // Default response
        return this.getDefaultResponse();
    }

    containsKeywords(message, keywords) {
        return keywords.some(keyword => message.includes(keyword));
    }

    getServicesResponse() {
        return {
            content: `
                <p>Great! We offer comprehensive software development services:</p>
                <p><strong>🌐 Web Development</strong><br>
                Custom web applications, e-commerce platforms, PWAs</p>
                <p><strong>📱 Mobile Development</strong><br>
                iOS & Android apps, cross-platform solutions</p>
                <p><strong>☁️ DevOps & Cloud</strong><br>
                Cloud migration, CI/CD pipelines, infrastructure automation</p>
                <p><strong>💼 Consulting & Strategy</strong><br>
                Technical architecture, code audits, digital transformation</p>
                <p>Which area interests you most?</p>
            `,
            options: {
                quickActions: [
                    { label: 'Web Development', value: 'web', icon: 'fas fa-globe' },
                    { label: 'Mobile Apps', value: 'mobile', icon: 'fas fa-mobile-alt' },
                    { label: 'DevOps & Cloud', value: 'devops', icon: 'fas fa-cloud' },
                    { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' }
                ]
            }
        };
    }

    getQuoteResponse() {
        return {
            content: `
                <p>I'd be happy to help you get a project quote! Let me gather some information:</p>
                <p><strong>What type of project are you planning?</strong></p>
            `,
            options: {
                quickActions: [
                    { label: 'New Website', value: 'quote_web', icon: 'fas fa-globe' },
                    { label: 'Mobile App', value: 'quote_mobile', icon: 'fas fa-mobile-alt' },
                    { label: 'System Upgrade', value: 'quote_upgrade', icon: 'fas fa-sync' },
                    { label: 'Talk to Expert', value: 'human', icon: 'fas fa-user' }
                ]
            }
        };
    }

    getPortfolioResponse() {
        return {
            content: `
                <p><strong>Our Recent Work 🎯</strong></p>
                <p><strong>🏥 HealthTech Platform</strong><br>
                Patient management system, HIPAA-compliant, 50K+ users</p>
                <p><strong>💰 FinTech Dashboard</strong><br>
                Real-time trading platform, advanced analytics, 99.9% uptime</p>
                <p><strong>🛒 E-commerce Marketplace</strong><br>
                Multi-vendor platform, $2M+ monthly transactions</p>
                <p>Would you like to see more details or discuss a similar project?</p>
            `,
            options: {
                quickActions: [
                    { label: 'View Portfolio', value: 'portfolio_link', icon: 'fas fa-external-link-alt' },
                    { label: 'Similar Project', value: 'similar', icon: 'fas fa-handshake' },
                    { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' },
                    { label: 'Talk to Expert', value: 'human', icon: 'fas fa-user' }
                ]
            }
        };
    }

    getHumanHandoffResponse() {
        return {
            content: `
                <p>I'd be happy to connect you with one of our solution architects!</p>
                <p><strong>Available Options:</strong></p>
                <p>📞 <strong>Immediate Call</strong> - Available Mon-Fri, 9 AM - 6 PM PST</p>
                <p>📅 <strong>Schedule Consultation</strong> - 45-minute detailed discussion</p>
                <p>💬 <strong>Continue via Email</strong> - Get detailed information packet</p>
                <p>What works best for you?</p>
            `,
            options: {
                quickActions: [
                    { label: 'Call Now', value: 'call_now', icon: 'fas fa-phone' },
                    { label: 'Schedule Meeting', value: 'schedule', icon: 'fas fa-calendar' },
                    { label: 'Email Info', value: 'email', icon: 'fas fa-envelope' },
                    { label: 'Continue Chat', value: 'continue', icon: 'fas fa-comments' }
                ]
            }
        };
    }

    getDefaultResponse() {
        return {
            content: `
                <p>I want to make sure I understand you correctly.</p>
                <p>I can help you with:</p>
                <p>• Learning about our services<br>
                • Getting project quotes<br>
                • Viewing our portfolio<br>
                • Connecting with our team</p>
                <p>What would you like to know more about?</p>
            `,
            options: {
                quickActions: [
                    { label: 'Our Services', value: 'services', icon: 'fas fa-code' },
                    { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' },
                    { label: 'Portfolio', value: 'portfolio', icon: 'fas fa-briefcase' },
                    { label: 'Talk to Human', value: 'human', icon: 'fas fa-user' }
                ]
            }
        };
    }

    handleQuickAction(action) {
        switch (action) {
            case 'services':
                this.addMessage('Tell me about your services', 'user');
                setTimeout(() => this.processUserMessage('services'), 500);
                break;
            case 'quote':
                this.addMessage('I need a project quote', 'user');
                setTimeout(() => this.processUserMessage('quote'), 500);
                break;
            case 'portfolio':
                this.addMessage('Show me your portfolio', 'user');
                setTimeout(() => this.processUserMessage('portfolio'), 500);
                break;
            case 'human':
                this.addMessage('I want to talk to a human', 'user');
                setTimeout(() => this.processUserMessage('human'), 500);
                break;
            case 'portfolio_link':
                this.addMessage('I want to view your full portfolio', 'user');
                setTimeout(() => {
                    this.addMessage('You can view our complete portfolio at <a href="/portfolio" target="_blank" style="color: var(--chatbot-primary);">technoloway.com/portfolio</a>. Would you like me to connect you with our team to discuss your project?', 'bot', {
                        quickActions: [
                            { label: 'Yes, connect me', value: 'human', icon: 'fas fa-user' },
                            { label: 'Get Quote First', value: 'quote', icon: 'fas fa-calculator' }
                        ]
                    });
                }, 500);
                break;
            case 'all_projects':
                this.addMessage('Show me all your projects', 'user');
                this.showTypingIndicator();
                this.loadProjectsData();
                break;
            case 'team':
                this.addMessage('Tell me about your team', 'user');
                this.showTypingIndicator();
                this.loadTeamData();
                break;
            case 'blog_link':
                this.addMessage('I want to read your blog', 'user');
                setTimeout(() => {
                    this.addMessage('You can read our latest insights and technical articles at <a href="/blog" target="_blank" style="color: var(--chatbot-primary);">technoloway.com/blog</a>. We regularly share industry insights, tutorials, and project case studies!', 'bot', {
                        quickActions: [
                            { label: 'Latest Articles', value: 'blog', icon: 'fas fa-newspaper' },
                            { label: 'Tech Insights', value: 'tech_insights', icon: 'fas fa-lightbulb' },
                            { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' }
                        ]
                    });
                }, 500);
                break;
            case 'all_testimonials':
                this.addMessage('Show me all client reviews', 'user');
                this.showTypingIndicator();
                this.loadTestimonialsData();
                break;
            case 'call_now':
            case 'schedule':
            case 'email':
                this.addMessage(`I'd like to ${action.replace('_', ' ')}`, 'user');
                this.showContactModal();
                break;
            default:
                this.addMessage(`I'd like to know more about ${action}`, 'user');
                setTimeout(() => this.processUserMessage(action), 500);
        }
    }

    setupAutoGreeting() {
        // Show notification badge after 5 seconds if chatbot hasn't been opened
        setTimeout(() => {
            if (!this.isOpen) {
                document.getElementById('notification-badge').style.display = 'flex';
            }
        }, 5000);
    }

    // Data loading methods
    async loadProjectsData() {
        try {
            const response = await fetch('/api/chatbot/projects');
            if (response.ok) {
                const projects = await response.json();
                this.hideTypingIndicator();

                let content = '<p><strong>Our Complete Project Portfolio 🚀</strong></p>';
                projects.slice(0, 5).forEach(project => {
                    const techList = project.technologies.slice(0, 3).join(', ');
                    content += `<p><strong>${project.name}</strong><br>` +
                              `${project.description}<br>` +
                              `<em>Client: ${project.clientName} • Service: ${project.service}</em><br>` +
                              `<em>Technologies: ${techList}</em></p>`;
                });

                content += '<p>Interested in starting a similar project?</p>';

                this.addMessage(content, 'bot', {
                    quickActions: [
                        { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' },
                        { label: 'Talk to Expert', value: 'human', icon: 'fas fa-user' },
                        { label: 'Our Services', value: 'services', icon: 'fas fa-code' }
                    ]
                });
            } else {
                this.hideTypingIndicator();
                this.addMessage('I\'m having trouble loading our projects right now. Please visit our portfolio page or contact our team directly.', 'bot');
            }
        } catch (error) {
            console.error('Error loading projects:', error);
            this.hideTypingIndicator();
            this.addMessage('I\'m having trouble loading our projects right now. Please visit our portfolio page or contact our team directly.', 'bot');
        }
    }

    async loadTeamData() {
        try {
            const response = await fetch('/api/chatbot/team');
            if (response.ok) {
                const team = await response.json();
                this.hideTypingIndicator();

                let content = '<p><strong>Meet Our Expert Team 👥</strong></p>';
                team.slice(0, 4).forEach(member => {
                    content += `<p><strong>${member.name}</strong> - ${member.position}<br>` +
                              `${member.bio}</p>`;
                });

                content += '<p>Ready to work with our talented team?</p>';

                this.addMessage(content, 'bot', {
                    quickActions: [
                        { label: 'Start Project', value: 'quote', icon: 'fas fa-rocket' },
                        { label: 'Schedule Call', value: 'schedule', icon: 'fas fa-calendar' },
                        { label: 'Our Process', value: 'process', icon: 'fas fa-cogs' }
                    ]
                });
            } else {
                this.hideTypingIndicator();
                this.addMessage('I\'m having trouble loading team information right now. Please visit our about page or contact us directly.', 'bot');
            }
        } catch (error) {
            console.error('Error loading team:', error);
            this.hideTypingIndicator();
            this.addMessage('I\'m having trouble loading team information right now. Please visit our about page or contact us directly.', 'bot');
        }
    }

    async loadTestimonialsData() {
        try {
            const response = await fetch('/api/chatbot/testimonials');
            if (response.ok) {
                const testimonials = await response.json();
                this.hideTypingIndicator();

                let content = '<p><strong>What All Our Clients Say 💬</strong></p>';
                testimonials.slice(0, 4).forEach(testimonial => {
                    const stars = '⭐'.repeat(testimonial.rating);
                    content += `<p><strong>${testimonial.clientName}</strong> - ${testimonial.clientPosition} at ${testimonial.clientCompany}<br>` +
                              `${stars}<br>` +
                              `"${testimonial.content}"</p>`;
                });

                content += '<p>Join our growing list of satisfied clients!</p>';

                this.addMessage(content, 'bot', {
                    quickActions: [
                        { label: 'Start Project', value: 'quote', icon: 'fas fa-rocket' },
                        { label: 'Talk to Expert', value: 'human', icon: 'fas fa-user' },
                        { label: 'Our Services', value: 'services', icon: 'fas fa-code' }
                    ]
                });
            } else {
                this.hideTypingIndicator();
                this.addMessage('I\'m having trouble loading testimonials right now. Please visit our testimonials page or contact us directly.', 'bot');
            }
        } catch (error) {
            console.error('Error loading testimonials:', error);
            this.hideTypingIndicator();
            this.addMessage('I\'m having trouble loading testimonials right now. Please visit our testimonials page or contact us directly.', 'bot');
        }
    }

    showContactModal() {
        // Create contact modal if it doesn't exist
        if (!document.getElementById('contact-modal')) {
            this.createContactModal();
        }
        document.getElementById('contact-modal').classList.remove('hidden');
    }

    createContactModal() {
        const modalHTML = `
            <div id="contact-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Contact Information</h3>
                        <button class="close-modal" onclick="window.technolowayChatbot.closeContactModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="contact-name">Name *</label>
                            <input type="text" id="contact-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-email">Email *</label>
                            <input type="email" id="contact-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-phone">Phone</label>
                            <input type="tel" id="contact-phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="contact-company">Company</label>
                            <input type="text" id="contact-company" name="company">
                        </div>
                        <div class="form-group">
                            <label for="contact-method">Preferred Contact Method</label>
                            <select id="contact-method" name="contactMethod">
                                <option value="email">Email</option>
                                <option value="phone">Phone Call</option>
                                <option value="video">Video Call</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contact-time">Best Time to Contact</label>
                            <select id="contact-time" name="contactTime">
                                <option value="morning">Morning (9 AM - 12 PM)</option>
                                <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
                                <option value="evening">Evening (5 PM - 8 PM)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contact-message">Additional Message</label>
                            <textarea id="contact-message" name="message" rows="3" placeholder="Tell us about your project..."></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="window.technolowayChatbot.closeContactModal()">Cancel</button>
                            <button type="submit" class="btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add form submission handler
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleContactSubmission();
        });
    }

    closeContactModal() {
        document.getElementById('contact-modal').classList.add('hidden');
    }

    async handleContactSubmission() {
        const form = document.getElementById('contact-form');
        const formData = new FormData(form);

        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            contactMethod: formData.get('contactMethod'),
            contactTime: formData.get('contactTime'),
            message: formData.get('message'),
            projectType: this.userContext.projectType,
            budgetRange: this.userContext.budgetRange,
            timeline: this.userContext.timeline
        };

        try {
            const response = await fetch('/api/chatbot/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            });

            if (response.ok) {
                this.closeContactModal();
                this.addMessage(
                    `Thank you, ${contactData.name}! I've received your contact information. Our team will reach out to you within 2 hours via ${contactData.contactMethod}. 🎉`,
                    'bot'
                );
                form.reset();
            } else {
                alert('There was an error submitting your information. Please try again or contact us directly.');
            }
        } catch (error) {
            console.error('Contact submission error:', error);
            alert('There was an error submitting your information. Please try again or contact us directly.');
        }
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.technolowayChatbot = new TechnoloWayChatbot();
});
