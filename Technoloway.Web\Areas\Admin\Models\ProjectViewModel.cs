using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class ProjectViewModel
{
    public ProjectViewModel()
    {
        Name = string.Empty;
        Description = string.Empty;
        ClientName = string.Empty;
        ImageUrl = string.Empty;
        ProjectUrl = string.Empty;
        IsFeatured = false;
        DisplayOrder = 0;
        CompletionDate = DateTime.UtcNow.AddMonths(1);
    }

    public int Id { get; set; }

    [Required(ErrorMessage = "Project name is required")]
    [StringLength(200, ErrorMessage = "Project name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Description is required")]
    [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
    public string Description { get; set; } = string.Empty;

    public string ClientName { get; set; } = string.Empty;

    // Current image URL - NOT required for form validation
    public string? ImageUrl { get; set; } = string.Empty;

    // For file upload
    [Display(Name = "Project Image")]
    public IFormFile? ImageFile { get; set; }

    [StringLength(500, ErrorMessage = "Project URL cannot exceed 500 characters")]
    public string ProjectUrl { get; set; } = string.Empty;

    [Required(ErrorMessage = "Completion date is required")]
    public DateTime CompletionDate { get; set; }

    public bool IsFeatured { get; set; } = false;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    // Navigation properties
    public int? ClientId { get; set; }
    public string? ClientCompanyName { get; set; }

    [Required(ErrorMessage = "Service is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service")]
    public int ServiceId { get; set; }
    public string? ServiceName { get; set; }

    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Convert from Entity to ViewModel
    public static ProjectViewModel FromEntity(Technoloway.Core.Entities.Project project)
    {
        return new ProjectViewModel
        {
            Id = project.Id,
            Name = project.Name,
            Description = project.Description,
            ClientName = project.ClientName,
            ImageUrl = project.ImageUrl,
            ProjectUrl = project.ProjectUrl,
            CompletionDate = project.CompletionDate,
            IsFeatured = project.IsFeatured,
            DisplayOrder = project.DisplayOrder,
            ClientId = project.ClientId,
            ClientCompanyName = project.Client?.CompanyName,
            ServiceId = project.ServiceId,
            ServiceName = project.Service?.Name,
            CreatedAt = project.CreatedAt,
            UpdatedAt = project.UpdatedAt
        };
    }

    // Convert from ViewModel to Entity
    public Technoloway.Core.Entities.Project ToEntity()
    {
        return new Technoloway.Core.Entities.Project
        {
            Id = this.Id,
            Name = this.Name ?? string.Empty,
            Description = this.Description ?? string.Empty,
            ClientName = this.ClientName ?? string.Empty,
            ImageUrl = this.ImageUrl ?? string.Empty,
            ProjectUrl = this.ProjectUrl ?? string.Empty,
            CompletionDate = this.CompletionDate,
            IsFeatured = this.IsFeatured,
            DisplayOrder = this.DisplayOrder,
            ClientId = this.ClientId,
            ServiceId = this.ServiceId,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }

    // Update existing entity with ViewModel data
    public void UpdateEntity(Technoloway.Core.Entities.Project project)
    {
        project.Name = this.Name ?? string.Empty;
        project.Description = this.Description ?? string.Empty;
        project.ClientName = this.ClientName ?? string.Empty;
        project.ImageUrl = this.ImageUrl ?? string.Empty;
        project.ProjectUrl = this.ProjectUrl ?? string.Empty;
        project.CompletionDate = this.CompletionDate;
        project.IsFeatured = this.IsFeatured;
        project.DisplayOrder = this.DisplayOrder;
        project.ClientId = this.ClientId;
        project.ServiceId = this.ServiceId;
    }
}
