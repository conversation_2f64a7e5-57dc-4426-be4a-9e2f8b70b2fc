using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ChatbotKeyword : BaseEntity
{
    [Required(ErrorMessage = "Keyword is required")]
    [StringLength(100, ErrorMessage = "Keyword cannot exceed 100 characters")]
    public string Keyword { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "Synonym cannot exceed 100 characters")]
    public string Synonyms { get; set; } = string.Empty; // Comma-separated synonyms

    [Range(1, 10, ErrorMessage = "Weight must be between 1 and 10")]
    public int Weight { get; set; } = 1; // Higher weight = more important for intent detection

    public bool IsActive { get; set; } = true;

    [StringLength(50, ErrorMessage = "Match type cannot exceed 50 characters")]
    public string MatchType { get; set; } = "contains"; // exact, contains, starts_with, ends_with

    public bool IsCaseSensitive { get; set; } = false;

    // Navigation properties
    [Required]
    public int ChatbotIntentId { get; set; }
    public virtual ChatbotIntent ChatbotIntent { get; set; } = null!;
}
