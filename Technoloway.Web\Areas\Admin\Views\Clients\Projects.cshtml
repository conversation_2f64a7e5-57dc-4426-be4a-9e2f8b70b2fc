@model IEnumerable<Technoloway.Core.Entities.Project>

@{
    ViewData["Title"] = "Client Projects";
    Layout = "_AdminLayout";
    var client = ViewBag.Client as Technoloway.Core.Entities.Client;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Projects for @(client != null ? client.CompanyName : "Client")</h1>
    <div>
        <a asp-area="Admin" asp-controller="Projects" asp-action="Create" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Project
        </a>
        <a asp-action="Details" asp-route-id="@(client != null ? client.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Client
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Clients
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">All Projects</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No projects have been created for this client yet.</p>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Service</th>
                            <th>Completion Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                                    {
                                        <img src="@item.ImageUrl" alt="@item.Name" class="img-thumbnail" style="max-height: 30px; margin-right: 10px;" />
                                    }
                                    @item.Name
                                </td>
                                <td>
                                    @if (item.Service != null)
                                    {
                                        @item.Service.Name
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </td>
                                <td>@item.CompletionDate.ToString("yyyy-MM-dd")</td>
                                <td>
                                    @if (item.CompletionDate > DateTime.UtcNow)
                                    {
                                        <span class="badge bg-primary">In Progress</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-success">Completed</span>
                                    }
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a asp-area="Admin" asp-controller="Projects" asp-action="Edit" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Projects" asp-action="Details" asp-route-id="@item.Id" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-area="Admin" asp-controller="Projects" asp-action="Delete" asp-route-id="@item.Id" class="btn btn-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });
    </script>
}
