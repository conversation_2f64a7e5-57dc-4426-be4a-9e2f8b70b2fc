using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class ProjectsController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public ProjectsController(
        IRepository<Project> projectRepository,
        IRepository<Service> serviceRepository,
        IRepository<Technology> technologyRepository,
        IHeroSectionRepository heroSectionRepository)
    {
        _projectRepository = projectRepository;
        _serviceRepository = serviceRepository;
        _technologyRepository = technologyRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index(int? serviceId = null, string? search = null)
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Projects");

        var query = _projectRepository.GetAll()
            .Where(p => !p.IsDeleted);

        if (serviceId.HasValue)
        {
            query = query.Where(p => p.ServiceId == serviceId.Value);
        }

        if (!string.IsNullOrEmpty(search))
        {
            query = query.Where(p => p.Name.Contains(search) || p.Description.Contains(search));
        }

        var projects = await query
            .OrderByDescending(p => p.CompletionDate)
            .ToListAsync();

        var services = await _serviceRepository.ListAsync(s => s.IsActive);

        var viewModel = new ProjectsIndexViewModel
        {
            Projects = projects,
            Services = services,
            SelectedServiceId = serviceId,
            SearchTerm = search
        };

        ViewBag.HeroSection = heroSection;
        ViewBag.PageName = "Portfolio";
        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        // Get related projects (same service, excluding current project)
        var relatedProjects = await _projectRepository.GetAll()
            .Where(p => p.ServiceId == project.ServiceId && p.Id != project.Id && !p.IsDeleted)
            .OrderByDescending(p => p.CompletionDate)
            .Take(3)
            .ToListAsync();

        // Get technologies (in a real app, this would use a many-to-many relationship)
        var technologies = await _technologyRepository.ListAllAsync();

        var viewModel = new ProjectDetailsViewModel
        {
            Project = project,
            RelatedProjects = relatedProjects,
            Technologies = technologies.AsEnumerable().Take(4).ToList() // Just for demo purposes
        };

        return View(viewModel);
    }
}
