@model Technoloway.Web.Models.ProjectsIndexViewModel

@{
    ViewData["Title"] = "Our Portfolio";
    ViewData["MetaDescription"] = "Explore our portfolio of successful projects across various industries. See how we've helped businesses achieve their goals with innovative software solutions.";
    ViewData["MetaKeywords"] = "portfolio, projects, case studies, software development, web applications, mobile apps";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
<partial name="_HeroSection" />

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header fixed-height-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Portfolio</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Our</span> Portfolio
                        </h1>
                        <p class="page-subtitle">
                            Explore our portfolio of successful projects across various industries. See how we've helped businesses achieve their goals with innovative software solutions.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Portfolio Introduction - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Our</span> Work
                    </h2>
                    <p class="section-subtitle">
                        We've helped businesses of all sizes achieve their goals with innovative software solutions.
                        Browse our portfolio to see examples of our work across various industries and technologies.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Filters - Modern Design -->
<section class="modern-section filter-section">
    <div class="container">
        <div class="modern-card filter-card animate-on-scroll">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 mb-3 mb-md-0">
                        <h4 class="filter-title mb-3">Filter by Service</h4>
                        <div class="filter-buttons">
                            <a href="@Url.Action("Index")" class="filter-btn @(Model.SelectedServiceId == null ? "active" : "")">
                                <i class="fas fa-th-large"></i>
                                <span>All Projects</span>
                            </a>
                            @foreach (var service in Model.Services)
                            {
                                <a href="@Url.Action("Index", new { serviceId = service.Id })" class="filter-btn @(Model.SelectedServiceId == service.Id ? "active" : "")">
                                    <span>@service.Name</span>
                                </a>
                            }
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4 class="filter-title mb-3">Search Projects</h4>
                        <form method="get" action="@Url.Action("Index")">
                            @if (Model.SelectedServiceId.HasValue)
                            {
                                <input type="hidden" name="serviceId" value="@Model.SelectedServiceId" />
                            }
                            <div class="search-input-wrapper">
                                <input type="text" class="search-input"
                                       placeholder="Search projects..." name="search" value="@Model.SearchTerm">
                                <button type="submit" class="search-btn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid - Modern Design -->
<section class="modern-section">
    <div class="container">
        @if (Model.Projects != null && Model.Projects.Any())
        {
            <div class="row g-4">
                @{
                    int projectIndex = 0;
                }
                @foreach (var project in Model.Projects)
                {
                    <div class="col-md-6 col-lg-4">
                        <div class="modern-card project-card animate-on-scroll" data-delay="@((projectIndex % 3 + 1) * 100)">
                            <div class="project-image-wrapper">
                                <img src="@project.ImageUrl" alt="@project.Name" class="project-featured-image">
                                <div class="project-overlay">
                                    <div class="project-overlay-content">
                                        <a asp-action="Details" asp-route-id="@project.Id" class="modern-btn primary">
                                            <span class="btn-text">View Details</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-eye"></i>
                                            </span>
                                        </a>
                                    </div>
                                </div>
                                <div class="project-service-badge">
                                    <span class="service-tag">
                                        @(project.Service?.Name ?? "Other")
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <h3 class="card-title">@project.Name</h3>
                                <p class="card-subtitle mb-4">
                                    @project.Description.Substring(0, Math.Min(project.Description.Length, 100))...
                                </p>
                                <div class="project-meta">
                                    <div class="project-date">
                                        <i class="fas fa-calendar"></i>
                                        <span>@project.CompletionDate.ToString("MMM yyyy")</span>
                                    </div>
                                    <a asp-action="Details" asp-route-id="@project.Id" class="project-link">
                                        <span>View Details</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    projectIndex++;
                }
            </div>
        }
        else
        {
            <div class="modern-card empty-state-card animate-on-scroll">
                <div class="card-body text-center">
                    <div class="empty-state-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="card-title">No Projects Found</h3>
                    <p class="card-subtitle mb-4">No projects found matching your criteria. Please try a different search or filter.</p>
                    <a href="@Url.Action("Index")" class="modern-btn primary">
                        <span class="btn-text">View All Projects</span>
                        <span class="btn-icon">
                            <i class="fas fa-th-large"></i>
                        </span>
                    </a>
                </div>
            </div>
        }
    </div>
</section>

<!-- CTA Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="modern-card cta-card animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0 text-center text-lg-start">
                        <h2 class="section-title mb-3">
                            <span class="title-highlight">Ready to Start</span> Your Project?
                        </h2>
                        <p class="section-subtitle mb-0">
                            Contact us today to discuss how we can help you achieve your business goals with innovative software solutions.
                        </p>
                    </div>
                    <div class="col-lg-4 text-center text-lg-end">
                        <div class="d-flex gap-3 justify-content-center justify-content-lg-end flex-wrap">
                            <a asp-controller="Home" asp-action="Contact" class="modern-btn primary">
                                <span class="btn-text">Get in Touch</span>
                                <span class="btn-icon">
                                    <i class="fas fa-phone"></i>
                                </span>
                            </a>
                            <a href="#portfolio" class="modern-btn secondary">
                                <span class="btn-text">View Portfolio</span>
                                <span class="btn-icon">
                                    <i class="fas fa-arrow-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Project card hover effects
            const projectCards = document.querySelectorAll('.project-card');

            projectCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                    const image = this.querySelector('.project-featured-image');
                    const overlay = this.querySelector('.project-overlay');
                    if (image) {
                        image.style.transform = 'scale(1.05)';
                    }
                    if (overlay) {
                        overlay.style.opacity = '1';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    const image = this.querySelector('.project-featured-image');
                    const overlay = this.querySelector('.project-overlay');
                    if (image) {
                        image.style.transform = 'scale(1)';
                    }
                    if (overlay) {
                        overlay.style.opacity = '0';
                    }
                });
            });

            // Filter button hover effects
            const filterBtns = document.querySelectorAll('.filter-btn');

            filterBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(-2px)';
                    }
                });

                btn.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(0)';
                    }
                });
            });

            // Project link hover effects
            const projectLinks = document.querySelectorAll('.project-link');

            projectLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'translateX(5px)';
                    }
                });

                link.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.style.transform = 'translateX(0)';
                    }
                });
            });

            // Search input focus effects
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.addEventListener('focus', function() {
                    this.parentElement.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
                });

                searchInput.addEventListener('blur', function() {
                    this.parentElement.style.boxShadow = 'none';
                });
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}
