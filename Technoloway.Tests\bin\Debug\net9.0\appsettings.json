{"ConnectionStrings": {"DefaultConnection": "DataSource=app.db;Cache=Shared"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Stripe": {"PublishableKey": "", "SecretKey": "", "WebhookSecret": ""}, "GoogleMaps": {"ApiKey": ""}, "Security": {"RequireEmailConfirmation": true, "EnableAccountLockout": true, "MaxFailedAccessAttempts": 5, "LockoutTimeSpan": "00:15:00", "PasswordRequireDigit": true, "PasswordRequireLowercase": true, "PasswordRequireUppercase": true, "PasswordRequireNonAlphanumeric": true, "PasswordRequiredLength": 8, "SessionTimeoutMinutes": 30}, "FileUpload": {"MaxFileSizeBytes": 5242880, "AllowedImageExtensions": [".jpg", ".jpeg", ".png", ".gif", ".webp"], "AllowedDocumentExtensions": [".pdf", ".doc", ".docx", ".txt"], "UploadPath": "wwwroot/uploads", "ScanForViruses": false}}