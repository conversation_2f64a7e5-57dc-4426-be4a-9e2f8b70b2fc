using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Invoice : BaseEntity
{
    [StringLength(50, ErrorMessage = "Invoice number cannot exceed 50 characters")]
    public string InvoiceNumber { get; set; } = string.Empty;

    [Required(ErrorMessage = "Issue date is required")]
    public DateTime IssueDate { get; set; } = DateTime.UtcNow;

    [Required(ErrorMessage = "Due date is required")]
    public DateTime DueDate { get; set; }

    [Required(ErrorMessage = "Amount is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Amount must be greater than 0")]
    public decimal Amount { get; set; }

    [Range(0, 100, ErrorMessage = "Tax rate must be between 0 and 100")]
    public decimal TaxRate { get; set; }

    public decimal TaxAmount { get; set; }

    public decimal TotalAmount { get; set; }

    [Required(ErrorMessage = "Status is required")]
    [StringLength(20, ErrorMessage = "Status cannot exceed 20 characters")]
    public string Status { get; set; } = "Pending"; // Pending, Paid, Overdue, Cancelled

    [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
    public string Notes { get; set; } = string.Empty;

    // Navigation properties
    [Required(ErrorMessage = "Client is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid client")]
    public int ClientId { get; set; }
    public Client? Client { get; set; }

    public int? ProjectId { get; set; }
    public Project? Project { get; set; }

    public ICollection<Payment> Payments { get; set; } = new List<Payment>();
    public ICollection<InvoiceItem> Items { get; set; } = new List<InvoiceItem>();
}
