using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ChatbotQuickAction : BaseEntity
{
    [Required(ErrorMessage = "Label is required")]
    [StringLength(100, ErrorMessage = "Label cannot exceed 100 characters")]
    public string Label { get; set; } = string.Empty;

    [Required(ErrorMessage = "Value is required")]
    [StringLength(200, ErrorMessage = "Value cannot exceed 200 characters")]
    public string Value { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
    public string IconClass { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "Action type cannot exceed 50 characters")]
    public string ActionType { get; set; } = "intent"; // intent, url, function

    [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
    public string Url { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    [StringLength(100, ErrorMessage = "CSS class cannot exceed 100 characters")]
    public string CssClass { get; set; } = string.Empty;

    // Navigation properties
    public int? ChatbotResponseId { get; set; }
    public virtual ChatbotResponse? ChatbotResponse { get; set; }

    public int? ChatbotIntentId { get; set; }
    public virtual ChatbotIntent? ChatbotIntent { get; set; }
}
