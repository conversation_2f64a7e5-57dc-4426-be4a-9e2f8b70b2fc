using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class ChatbotController : ControllerBase
    {
        private readonly IRepository<Service> _serviceRepository;
        private readonly IRepository<Project> _projectRepository;
        private readonly IRepository<Technology> _technologyRepository;
        private readonly IRepository<Testimonial> _testimonialRepository;
        private readonly IRepository<BlogPost> _blogPostRepository;
        private readonly IRepository<TeamMember> _teamMemberRepository;
        private readonly IRepository<ContactForm> _contactFormRepository;
        private readonly IRepository<ChatbotIntent> _chatbotIntentRepository;
        private readonly IRepository<ChatbotResponse> _chatbotResponseRepository;
        private readonly IRepository<ChatbotKeyword> _chatbotKeywordRepository;
        private readonly IRepository<ChatbotQuickAction> _chatbotQuickActionRepository;

        public ChatbotController(
            IRepository<Service> serviceRepository,
            IRepository<Project> projectRepository,
            IRepository<Technology> technologyRepository,
            IRepository<Testimonial> testimonialRepository,
            IRepository<BlogPost> blogPostRepository,
            IRepository<TeamMember> teamMemberRepository,
            IRepository<ContactForm> contactFormRepository,
            IRepository<ChatbotIntent> chatbotIntentRepository,
            IRepository<ChatbotResponse> chatbotResponseRepository,
            IRepository<ChatbotKeyword> chatbotKeywordRepository,
            IRepository<ChatbotQuickAction> chatbotQuickActionRepository)
        {
            _serviceRepository = serviceRepository;
            _projectRepository = projectRepository;
            _technologyRepository = technologyRepository;
            _testimonialRepository = testimonialRepository;
            _blogPostRepository = blogPostRepository;
            _teamMemberRepository = teamMemberRepository;
            _contactFormRepository = contactFormRepository;
            _chatbotIntentRepository = chatbotIntentRepository;
            _chatbotResponseRepository = chatbotResponseRepository;
            _chatbotKeywordRepository = chatbotKeywordRepository;
            _chatbotQuickActionRepository = chatbotQuickActionRepository;
        }

        [HttpPost("message")]
        public async Task<IActionResult> ProcessMessage([FromBody] ChatbotMessageRequest request)
        {
            try
            {
                var intent = await DetectIntent(request.Message);
                var response = await GenerateResponse(intent, request.Message, request.Context);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to process message", details = ex.Message });
            }
        }

        [HttpGet("services")]
        public async Task<IActionResult> GetServices()
        {
            try
            {
                var services = await _serviceRepository.ListAsync(s => s.IsActive);
                var serviceData = services.OrderBy(s => s.DisplayOrder).Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Description,
                    s.IconClass,
                    s.Price,
                    ProjectCount = s.Projects?.Count ?? 0
                }).ToList();

                return Ok(serviceData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch services", details = ex.Message });
            }
        }

        [HttpGet("projects")]
        public async Task<IActionResult> GetProjects([FromQuery] bool featuredOnly = false)
        {
            try
            {
                var projects = featuredOnly 
                    ? await _projectRepository.ListAsync(p => p.IsFeatured)
                    : await _projectRepository.GetAll()
                        .Include(p => p.Service)
                        .Include(p => p.Technologies)
                        .Where(p => !p.IsDeleted)
                        .OrderByDescending(p => p.CompletionDate)
                        .Take(6)
                        .ToListAsync();

                var projectData = projects.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.ClientName,
                    p.ImageUrl,
                    p.ProjectUrl,
                    p.CompletionDate,
                    p.IsFeatured,
                    Service = p.Service?.Name,
                    Technologies = p.Technologies?.Select(t => t.Name).ToList() ?? new List<string>()
                }).ToList();

                return Ok(projectData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch projects", details = ex.Message });
            }
        }

        [HttpGet("technologies")]
        public async Task<IActionResult> GetTechnologies()
        {
            try
            {
                var technologies = await _technologyRepository.GetAll()
                    .Where(t => !t.IsDeleted)
                    .OrderBy(t => t.DisplayOrder)
                    .ToListAsync();

                var techData = technologies.Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Description,
                    t.IconUrl,
                    ProjectCount = t.Projects?.Count ?? 0
                }).ToList();

                return Ok(techData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch technologies", details = ex.Message });
            }
        }

        [HttpGet("testimonials")]
        public async Task<IActionResult> GetTestimonials()
        {
            try
            {
                var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);
                var testimonialData = testimonials.OrderBy(t => t.DisplayOrder).Select(t => new
                {
                    t.Id,
                    t.ClientName,
                    t.ClientCompany,
                    t.ClientTitle,
                    t.Content,
                    t.Rating,
                    t.ClientPhotoUrl
                }).ToList();

                return Ok(testimonialData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch testimonials", details = ex.Message });
            }
        }

        [HttpGet("blog-posts")]
        public async Task<IActionResult> GetBlogPosts([FromQuery] int count = 3)
        {
            try
            {
                var blogPosts = await _blogPostRepository.GetAll()
                    .Where(b => b.IsPublished && !b.IsDeleted)
                    .OrderByDescending(b => b.PublishedAt)
                    .Take(count)
                    .ToListAsync();

                var blogData = blogPosts.Select(b => new
                {
                    b.Id,
                    b.Title,
                    b.Excerpt,
                    b.Slug,
                    b.FeaturedImageUrl,
                    b.PublishedAt,
                    b.AuthorName,
                    Categories = b.Categories?.Split(',').Select(c => c.Trim()).ToList() ?? new List<string>()
                }).ToList();

                return Ok(blogData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch blog posts", details = ex.Message });
            }
        }

        [HttpGet("team")]
        public async Task<IActionResult> GetTeamMembers()
        {
            try
            {
                var teamMembers = await _teamMemberRepository.GetAll()
                    .Where(t => !t.IsDeleted)
                    .OrderBy(t => t.DisplayOrder)
                    .ToListAsync();

                var teamData = teamMembers.Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Position,
                    t.Bio,
                    t.PhotoUrl,
                    t.LinkedInUrl,
                    t.TwitterUrl
                }).ToList();

                return Ok(teamData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch team members", details = ex.Message });
            }
        }

        [HttpPost("contact")]
        public async Task<IActionResult> SubmitContact([FromBody] ChatbotContactRequest request)
        {
            try
            {
                var contactForm = new ContactForm
                {
                    Name = request.Name,
                    Email = request.Email,
                    Phone = request.Phone ?? string.Empty,
                    Subject = "Chatbot Inquiry",
                    Message = $"Contact Method: {request.ContactMethod}\n" +
                             $"Best Time: {request.ContactTime}\n" +
                             $"Company: {request.Company ?? "Not specified"}\n" +
                             $"Project Type: {request.ProjectType ?? "Not specified"}\n" +
                             $"Budget Range: {request.BudgetRange ?? "Not specified"}\n" +
                             $"Timeline: {request.Timeline ?? "Not specified"}\n\n" +
                             $"Additional Message: {request.Message ?? "None"}",
                    IsRead = false,
                    CreatedAt = DateTime.UtcNow
                };

                await _contactFormRepository.AddAsync(contactForm);

                return Ok(new { success = true, message = "Contact information received successfully!" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to submit contact form", details = ex.Message });
            }
        }

        private async Task<string> DetectIntent(string message)
        {
            var lowerMessage = message.ToLower();
            var allKeywords = await _chatbotKeywordRepository.GetAll()
                .Include(k => k.ChatbotIntent)
                .Where(k => k.IsActive && k.ChatbotIntent.IsActive)
                .ToListAsync();

            var intentScores = new Dictionary<string, int>();

            foreach (var keyword in allKeywords)
            {
                var keywordText = keyword.IsCaseSensitive ? keyword.Keyword : keyword.Keyword.ToLower();
                var messageToCheck = keyword.IsCaseSensitive ? message : lowerMessage;

                bool matches = keyword.MatchType switch
                {
                    "exact" => messageToCheck.Equals(keywordText),
                    "starts_with" => messageToCheck.StartsWith(keywordText),
                    "ends_with" => messageToCheck.EndsWith(keywordText),
                    _ => messageToCheck.Contains(keywordText)
                };

                if (matches)
                {
                    var intentName = keyword.ChatbotIntent.Name;
                    if (!intentScores.ContainsKey(intentName))
                        intentScores[intentName] = 0;
                    intentScores[intentName] += keyword.Weight;
                }

                // Check synonyms
                if (!string.IsNullOrEmpty(keyword.Synonyms))
                {
                    var synonyms = keyword.Synonyms.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var synonym in synonyms)
                    {
                        var synonymText = keyword.IsCaseSensitive ? synonym.Trim() : synonym.Trim().ToLower();
                        bool synonymMatches = keyword.MatchType switch
                        {
                            "exact" => messageToCheck.Equals(synonymText),
                            "starts_with" => messageToCheck.StartsWith(synonymText),
                            "ends_with" => messageToCheck.EndsWith(synonymText),
                            _ => messageToCheck.Contains(synonymText)
                        };

                        if (synonymMatches)
                        {
                            var intentName = keyword.ChatbotIntent.Name;
                            if (!intentScores.ContainsKey(intentName))
                                intentScores[intentName] = 0;
                            intentScores[intentName] += keyword.Weight;
                        }
                    }
                }
            }

            // Return the intent with the highest score, or "general" if no matches
            return intentScores.Any()
                ? intentScores.OrderByDescending(kvp => kvp.Value).First().Key
                : "general";
        }

        private bool ContainsKeywords(string message, string[] keywords)
        {
            return keywords.Any(keyword => message.Contains(keyword));
        }

        private async Task<ChatbotResponse> GenerateResponse(string intent, string message, ChatbotContext? context)
        {
            // Try to get response from database first
            var dbResponse = await GetDatabaseResponse(intent);
            if (dbResponse != null)
            {
                return dbResponse;
            }

            // Fallback to hardcoded responses for backward compatibility
            return intent switch
            {
                "services" => await GetServicesResponse(),
                "portfolio" => await GetPortfolioResponse(),
                "technologies" => await GetTechnologiesResponse(),
                "testimonials" => await GetTestimonialsResponse(),
                "about" => await GetAboutResponse(),
                "blog" => await GetBlogResponse(),
                "quote" => GetQuoteResponse(),
                "human" => GetHumanHandoffResponse(),
                _ => GetDefaultResponse()
            };
        }

        private async Task<ChatbotResponse?> GetDatabaseResponse(string intentName)
        {
            try
            {
                var intent = await _chatbotIntentRepository.GetAll()
                    .Include(i => i.Responses.Where(r => r.IsActive))
                    .ThenInclude(r => r.QuickActions.Where(qa => qa.IsActive))
                    .FirstOrDefaultAsync(i => i.Name == intentName && i.IsActive);

                if (intent == null || !intent.Responses.Any())
                {
                    return null;
                }

                // Get the first active response (you can add logic for multiple responses)
                var response = intent.Responses.OrderBy(r => r.DisplayOrder).First();

                // Process template variables if any
                var content = await ProcessTemplateVariables(response.Content, response.TemplateVariables);

                return new ChatbotResponse
                {
                    Content = content,
                    Intent = intentName,
                    QuickActions = response.QuickActions.OrderBy(qa => qa.DisplayOrder).Select(qa => new QuickAction
                    {
                        Label = qa.Label,
                        Value = qa.Value,
                        Icon = qa.IconClass
                    }).ToList()
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        private async Task<string> ProcessTemplateVariables(string content, string? templateVariables)
        {
            if (string.IsNullOrEmpty(templateVariables))
            {
                return content;
            }

            try
            {
                // Simple template variable processing
                // You can extend this to support more complex templating
                var processedContent = content;

                // Replace common variables with dynamic data
                if (content.Contains("{{service_count}}"))
                {
                    var serviceCount = await _serviceRepository.CountAsync(s => s.IsActive);
                    processedContent = processedContent.Replace("{{service_count}}", serviceCount.ToString());
                }

                if (content.Contains("{{project_count}}"))
                {
                    var projectCount = await _projectRepository.CountAsync(p => !p.IsDeleted);
                    processedContent = processedContent.Replace("{{project_count}}", projectCount.ToString());
                }

                if (content.Contains("{{team_count}}"))
                {
                    var teamCount = await _teamMemberRepository.CountAsync();
                    processedContent = processedContent.Replace("{{team_count}}", teamCount.ToString());
                }

                if (content.Contains("{{client_count}}"))
                {
                    var clientCount = await _projectRepository.GetAll()
                        .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.ClientName))
                        .Select(p => p.ClientName)
                        .Distinct()
                        .CountAsync();
                    processedContent = processedContent.Replace("{{client_count}}", clientCount.ToString());
                }

                return processedContent;
            }
            catch (Exception)
            {
                return content; // Return original content if template processing fails
            }
        }

        private async Task<ChatbotResponse> GetServicesResponse()
        {
            var services = await _serviceRepository.ListAsync(s => s.IsActive);
            var serviceList = services.OrderBy(s => s.DisplayOrder).Take(4).ToList();

            var content = "<p>Great! Here are our main software development services:</p>";
            
            foreach (var service in serviceList)
            {
                var icon = GetServiceIcon(service.Name);
                content += $"<p><strong>{icon} {service.Name}</strong><br>{service.Description}</p>";
            }

            content += "<p>Which service interests you most?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "services",
                QuickActions = serviceList.Select(s => new QuickAction
                {
                    Label = s.Name,
                    Value = $"service_{s.Id}",
                    Icon = "fas fa-code"
                }).ToList()
            };
        }

        private async Task<ChatbotResponse> GetPortfolioResponse()
        {
            var projects = await _projectRepository.GetAll()
                .Include(p => p.Service)
                .Include(p => p.Technologies)
                .Where(p => p.IsFeatured && !p.IsDeleted)
                .OrderByDescending(p => p.CompletionDate)
                .Take(3)
                .ToListAsync();

            var content = "<p><strong>Our Recent Featured Work 🎯</strong></p>";

            foreach (var project in projects)
            {
                var techList = string.Join(", ", project.Technologies?.Take(3).Select(t => t.Name) ?? new List<string>());
                content += $"<p><strong>🚀 {project.Name}</strong><br>" +
                          $"{project.Description}<br>" +
                          $"<em>Client: {project.ClientName}</em><br>" +
                          $"<em>Technologies: {techList}</em></p>";
            }

            content += "<p>Would you like to see more projects or discuss a similar one?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "portfolio",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "View All Projects", Value = "all_projects", Icon = "fas fa-external-link-alt" },
                    new() { Label = "Similar Project", Value = "similar", Icon = "fas fa-handshake" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Talk to Expert", Value = "human", Icon = "fas fa-user" }
                }
            };
        }

        private async Task<ChatbotResponse> GetTechnologiesResponse()
        {
            var technologies = await _technologyRepository.GetAll()
                .Where(t => !t.IsDeleted)
                .OrderBy(t => t.DisplayOrder)
                .Take(8)
                .ToListAsync();

            var content = "<p><strong>Our Technology Stack 💻</strong></p>";
            content += "<p>We work with cutting-edge technologies to deliver the best solutions:</p>";

            var techGroups = technologies.GroupBy(t => GetTechCategory(t.Name)).Take(4);
            foreach (var group in techGroups)
            {
                var techNames = string.Join(", ", group.Select(t => t.Name));
                content += $"<p><strong>{group.Key}:</strong> {techNames}</p>";
            }

            content += "<p>Need a specific technology for your project?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "technologies",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Web Technologies", Value = "web_tech", Icon = "fas fa-globe" },
                    new() { Label = "Mobile Technologies", Value = "mobile_tech", Icon = "fas fa-mobile-alt" },
                    new() { Label = "Cloud & DevOps", Value = "cloud_tech", Icon = "fas fa-cloud" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" }
                }
            };
        }

        private async Task<ChatbotResponse> GetTestimonialsResponse()
        {
            var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);
            var topTestimonials = testimonials.OrderByDescending(t => t.Rating).Take(2).ToList();

            var content = "<p><strong>What Our Clients Say 💬</strong></p>";

            foreach (var testimonial in topTestimonials)
            {
                var stars = string.Concat(Enumerable.Repeat("⭐", testimonial.Rating));
                content += $"<p><strong>{testimonial.ClientName}</strong> - {testimonial.ClientTitle} at {testimonial.ClientCompany}<br>" +
                          $"{stars}<br>" +
                          $"\"{testimonial.Content}\"</p>";
            }

            content += "<p>Ready to join our satisfied clients?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "testimonials",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Start Project", Value = "quote", Icon = "fas fa-rocket" },
                    new() { Label = "View All Reviews", Value = "all_testimonials", Icon = "fas fa-star" },
                    new() { Label = "Talk to Expert", Value = "human", Icon = "fas fa-user" },
                    new() { Label = "Our Services", Value = "services", Icon = "fas fa-code" }
                }
            };
        }

        private async Task<ChatbotResponse> GetAboutResponse()
        {
            var teamCount = await _teamMemberRepository.CountAsync();
            var projectCount = await _projectRepository.CountAsync();
            var clientCount = await _projectRepository.GetAll()
                .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.ClientName))
                .Select(p => p.ClientName)
                .Distinct()
                .CountAsync();

            var content = $"<p><strong>About TechnoloWay 🏢</strong></p>" +
                         $"<p>We're a passionate software development company founded in 2018, dedicated to delivering innovative digital solutions.</p>" +
                         $"<p><strong>Our Numbers:</strong><br>" +
                         $"👥 {teamCount}+ Expert Developers<br>" +
                         $"🚀 {projectCount}+ Successful Projects<br>" +
                         $"🤝 {clientCount}+ Happy Clients<br>" +
                         $"📍 Based in San Francisco, CA (Remote-friendly)</p>" +
                         $"<p><strong>Our Expertise:</strong><br>" +
                         $"• Full-stack development<br>" +
                         $"• Cloud-native solutions<br>" +
                         $"• Agile methodology<br>" +
                         $"• 24/7 support available</p>" +
                         $"<p>Want to learn more about our team or start a project?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "about",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Meet Our Team", Value = "team", Icon = "fas fa-users" },
                    new() { Label = "Our Process", Value = "process", Icon = "fas fa-cogs" },
                    new() { Label = "Start Project", Value = "quote", Icon = "fas fa-rocket" },
                    new() { Label = "Contact Us", Value = "human", Icon = "fas fa-envelope" }
                }
            };
        }

        private async Task<ChatbotResponse> GetBlogResponse()
        {
            var blogPosts = await _blogPostRepository.GetAll()
                .Where(b => b.IsPublished && !b.IsDeleted)
                .OrderByDescending(b => b.PublishedAt)
                .Take(3)
                .ToListAsync();

            var content = "<p><strong>Latest Insights & Articles 📚</strong></p>";

            foreach (var post in blogPosts)
            {
                content += $"<p><strong>📖 {post.Title}</strong><br>" +
                          $"{post.Excerpt}<br>" +
                          $"<em>By {post.AuthorName} • {post.PublishedAt?.ToString("MMM dd, yyyy")}</em></p>";
            }

            content += "<p>Want to read more or discuss your project?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "blog",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Read Blog", Value = "blog_link", Icon = "fas fa-external-link-alt" },
                    new() { Label = "Tech Insights", Value = "tech_insights", Icon = "fas fa-lightbulb" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Talk to Expert", Value = "human", Icon = "fas fa-user" }
                }
            };
        }

        private ChatbotResponse GetQuoteResponse()
        {
            return new ChatbotResponse
            {
                Content = "<p>I'd be happy to help you get a project quote! Let me gather some information:</p><p><strong>What type of project are you planning?</strong></p>",
                Intent = "quote",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "New Website", Value = "quote_web", Icon = "fas fa-globe" },
                    new() { Label = "Mobile App", Value = "quote_mobile", Icon = "fas fa-mobile-alt" },
                    new() { Label = "System Upgrade", Value = "quote_upgrade", Icon = "fas fa-sync" },
                    new() { Label = "Talk to Expert", Value = "human", Icon = "fas fa-user" }
                }
            };
        }

        private ChatbotResponse GetHumanHandoffResponse()
        {
            return new ChatbotResponse
            {
                Content = "<p>I'd be happy to connect you with one of our solution architects!</p><p><strong>Available Options:</strong></p><p>📞 <strong>Immediate Call</strong> - Available Mon-Fri, 9 AM - 6 PM PST</p><p>📅 <strong>Schedule Consultation</strong> - 45-minute detailed discussion</p><p>💬 <strong>Continue via Email</strong> - Get detailed information packet</p><p>What works best for you?</p>",
                Intent = "human",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Call Now", Value = "call_now", Icon = "fas fa-phone" },
                    new() { Label = "Schedule Meeting", Value = "schedule", Icon = "fas fa-calendar" },
                    new() { Label = "Email Info", Value = "email", Icon = "fas fa-envelope" },
                    new() { Label = "Continue Chat", Value = "continue", Icon = "fas fa-comments" }
                }
            };
        }

        private ChatbotResponse GetDefaultResponse()
        {
            return new ChatbotResponse
            {
                Content = "<p>I want to make sure I understand you correctly.</p><p>I can help you with:</p><p>• Learning about our services<br>• Getting project quotes<br>• Viewing our portfolio<br>• Connecting with our team</p><p>What would you like to know more about?</p>",
                Intent = "general",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Our Services", Value = "services", Icon = "fas fa-code" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Portfolio", Value = "portfolio", Icon = "fas fa-briefcase" },
                    new() { Label = "Talk to Human", Value = "human", Icon = "fas fa-user" }
                }
            };
        }

        private string GetServiceIcon(string serviceName)
        {
            return serviceName.ToLower() switch
            {
                var name when name.Contains("web") => "🌐",
                var name when name.Contains("mobile") => "📱",
                var name when name.Contains("cloud") || name.Contains("devops") => "☁️",
                var name when name.Contains("consult") => "💼",
                var name when name.Contains("design") => "🎨",
                var name when name.Contains("data") => "📊",
                _ => "🚀"
            };
        }

        private string GetTechCategory(string techName)
        {
            return techName.ToLower() switch
            {
                var name when name.Contains("react") || name.Contains("vue") || name.Contains("angular") || name.Contains("javascript") || name.Contains("html") || name.Contains("css") => "Frontend",
                var name when name.Contains("node") || name.Contains("python") || name.Contains("java") || name.Contains("c#") || name.Contains(".net") || name.Contains("php") => "Backend",
                var name when name.Contains("aws") || name.Contains("azure") || name.Contains("docker") || name.Contains("kubernetes") || name.Contains("jenkins") => "Cloud & DevOps",
                var name when name.Contains("mysql") || name.Contains("postgresql") || name.Contains("mongodb") || name.Contains("redis") => "Database",
                _ => "Other"
            };
        }
    }

    // Request/Response models
    public class ChatbotMessageRequest
    {
        public string Message { get; set; } = string.Empty;
        public ChatbotContext? Context { get; set; }
    }

    public class ChatbotContext
    {
        public string? ProjectType { get; set; }
        public string? BudgetRange { get; set; }
        public string? Timeline { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
    }

    public class ChatbotContactRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string ContactMethod { get; set; } = "email";
        public string ContactTime { get; set; } = "morning";
        public string? ProjectType { get; set; }
        public string? BudgetRange { get; set; }
        public string? Timeline { get; set; }
        public string? Message { get; set; }
    }

    public class ChatbotResponse
    {
        public string Content { get; set; } = string.Empty;
        public string Intent { get; set; } = string.Empty;
        public List<QuickAction> QuickActions { get; set; } = new();
        public List<string> Suggestions { get; set; } = new();
    }

    public class QuickAction
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
