using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    public class ChatbotController : ControllerBase
    {
        private readonly IRepository<Service> _serviceRepository;
        private readonly IRepository<Project> _projectRepository;
        private readonly IRepository<Technology> _technologyRepository;
        private readonly IRepository<Testimonial> _testimonialRepository;
        private readonly IRepository<BlogPost> _blogPostRepository;
        private readonly IRepository<TeamMember> _teamMemberRepository;
        private readonly IRepository<ContactForm> _contactFormRepository;
        private readonly IRepository<ChatbotIntent> _chatbotIntentRepository;
        private readonly IRepository<Core.Entities.ChatbotResponse> _chatbotResponseRepository;
        private readonly IRepository<ChatbotKeyword> _chatbotKeywordRepository;
        private readonly IRepository<ChatbotQuickAction> _chatbotQuickActionRepository;

        public ChatbotController(
            IRepository<Service> serviceRepository,
            IRepository<Project> projectRepository,
            IRepository<Technology> technologyRepository,
            IRepository<Testimonial> testimonialRepository,
            IRepository<BlogPost> blogPostRepository,
            IRepository<TeamMember> teamMemberRepository,
            IRepository<ContactForm> contactFormRepository,
            IRepository<ChatbotIntent> chatbotIntentRepository,
            IRepository<Core.Entities.ChatbotResponse> chatbotResponseRepository,
            IRepository<ChatbotKeyword> chatbotKeywordRepository,
            IRepository<ChatbotQuickAction> chatbotQuickActionRepository)
        {
            _serviceRepository = serviceRepository;
            _projectRepository = projectRepository;
            _technologyRepository = technologyRepository;
            _testimonialRepository = testimonialRepository;
            _blogPostRepository = blogPostRepository;
            _teamMemberRepository = teamMemberRepository;
            _contactFormRepository = contactFormRepository;
            _chatbotIntentRepository = chatbotIntentRepository;
            _chatbotResponseRepository = chatbotResponseRepository;
            _chatbotKeywordRepository = chatbotKeywordRepository;
            _chatbotQuickActionRepository = chatbotQuickActionRepository;
        }

        [HttpPost("message")]
        public async Task<IActionResult> ProcessMessage([FromBody] ChatbotMessageRequest request)
        {
            try
            {
                var intent = await DetectIntent(request.Message);
                var response = await GenerateResponse(intent, request.Message, request.Context);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to process message", details = ex.Message });
            }
        }

        [HttpGet("services")]
        public async Task<IActionResult> GetServices()
        {
            try
            {
                var services = await _serviceRepository.ListAsync(s => s.IsActive);
                var serviceData = services.OrderBy(s => s.DisplayOrder).Select(s => new
                {
                    s.Id,
                    s.Name,
                    s.Description,
                    s.IconClass,
                    s.Price,
                    ProjectCount = s.Projects?.Count ?? 0
                }).ToList();

                return Ok(serviceData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch services", details = ex.Message });
            }
        }

        [HttpGet("projects")]
        public async Task<IActionResult> GetProjects([FromQuery] bool featuredOnly = false)
        {
            try
            {
                var projects = featuredOnly 
                    ? await _projectRepository.ListAsync(p => p.IsFeatured)
                    : await _projectRepository.GetAll()
                        .Include(p => p.Service)
                        .Include(p => p.Technologies)
                        .Where(p => !p.IsDeleted)
                        .OrderByDescending(p => p.CompletionDate)
                        .Take(6)
                        .ToListAsync();

                var projectData = projects.Select(p => new
                {
                    p.Id,
                    p.Name,
                    p.Description,
                    p.ClientName,
                    p.ImageUrl,
                    p.ProjectUrl,
                    p.CompletionDate,
                    p.IsFeatured,
                    Service = p.Service?.Name,
                    Technologies = p.Technologies?.Select(t => t.Name).ToList() ?? new List<string>()
                }).ToList();

                return Ok(projectData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch projects", details = ex.Message });
            }
        }

        [HttpGet("technologies")]
        public async Task<IActionResult> GetTechnologies()
        {
            try
            {
                var technologies = await _technologyRepository.GetAll()
                    .Where(t => !t.IsDeleted)
                    .OrderBy(t => t.DisplayOrder)
                    .ToListAsync();

                var techData = technologies.Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Description,
                    t.IconUrl,
                    ProjectCount = t.Projects?.Count ?? 0
                }).ToList();

                return Ok(techData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch technologies", details = ex.Message });
            }
        }

        [HttpGet("testimonials")]
        public async Task<IActionResult> GetTestimonials()
        {
            try
            {
                var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);
                var testimonialData = testimonials.OrderBy(t => t.DisplayOrder).Select(t => new
                {
                    t.Id,
                    t.ClientName,
                    t.ClientCompany,
                    t.ClientTitle,
                    t.Content,
                    t.Rating,
                    t.ClientPhotoUrl
                }).ToList();

                return Ok(testimonialData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch testimonials", details = ex.Message });
            }
        }

        [HttpGet("blog-posts")]
        public async Task<IActionResult> GetBlogPosts([FromQuery] int count = 3)
        {
            try
            {
                var blogPosts = await _blogPostRepository.GetAll()
                    .Where(b => b.IsPublished && !b.IsDeleted)
                    .OrderByDescending(b => b.PublishedAt)
                    .Take(count)
                    .ToListAsync();

                var blogData = blogPosts.Select(b => new
                {
                    b.Id,
                    b.Title,
                    b.Excerpt,
                    b.Slug,
                    b.FeaturedImageUrl,
                    b.PublishedAt,
                    b.AuthorName,
                    Categories = b.Categories?.Split(',').Select(c => c.Trim()).ToList() ?? new List<string>()
                }).ToList();

                return Ok(blogData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch blog posts", details = ex.Message });
            }
        }

        [HttpGet("team")]
        public async Task<IActionResult> GetTeamMembers()
        {
            try
            {
                var teamMembers = await _teamMemberRepository.GetAll()
                    .Where(t => !t.IsDeleted)
                    .OrderBy(t => t.DisplayOrder)
                    .ToListAsync();

                var teamData = teamMembers.Select(t => new
                {
                    t.Id,
                    t.Name,
                    t.Position,
                    t.Bio,
                    t.PhotoUrl,
                    t.LinkedInUrl,
                    t.TwitterUrl
                }).ToList();

                return Ok(teamData);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to fetch team members", details = ex.Message });
            }
        }

        [HttpPost("contact")]
        public async Task<IActionResult> SubmitContact([FromBody] ChatbotContactRequest request)
        {
            try
            {
                var contactForm = new ContactForm
                {
                    Name = request.Name,
                    Email = request.Email,
                    Phone = request.Phone ?? string.Empty,
                    Subject = "Chatbot Inquiry",
                    Message = $"Contact Method: {request.ContactMethod}\n" +
                             $"Best Time: {request.ContactTime}\n" +
                             $"Company: {request.Company ?? "Not specified"}\n" +
                             $"Project Type: {request.ProjectType ?? "Not specified"}\n" +
                             $"Budget Range: {request.BudgetRange ?? "Not specified"}\n" +
                             $"Timeline: {request.Timeline ?? "Not specified"}\n\n" +
                             $"Additional Message: {request.Message ?? "None"}",
                    IsRead = false,
                    CreatedAt = DateTime.UtcNow
                };

                await _contactFormRepository.AddAsync(contactForm);

                return Ok(new { success = true, message = "Contact information received successfully!" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { error = "Failed to submit contact form", details = ex.Message });
            }
        }

        private async Task<string> DetectIntent(string message)
        {
            var lowerMessage = message.ToLower();
            var allKeywords = await _chatbotKeywordRepository.GetAll()
                .Include(k => k.ChatbotIntent)
                .Where(k => k.IsActive && k.ChatbotIntent.IsActive)
                .ToListAsync();

            var intentScores = new Dictionary<string, int>();

            foreach (var keyword in allKeywords)
            {
                var keywordText = keyword.IsCaseSensitive ? keyword.Keyword : keyword.Keyword.ToLower();
                var messageToCheck = keyword.IsCaseSensitive ? message : lowerMessage;

                bool matches = keyword.MatchType switch
                {
                    "exact" => messageToCheck.Equals(keywordText),
                    "starts_with" => messageToCheck.StartsWith(keywordText),
                    "ends_with" => messageToCheck.EndsWith(keywordText),
                    _ => messageToCheck.Contains(keywordText)
                };

                if (matches)
                {
                    var intentName = keyword.ChatbotIntent.Name;
                    if (!intentScores.ContainsKey(intentName))
                        intentScores[intentName] = 0;
                    intentScores[intentName] += keyword.Weight;
                }

                // Check synonyms
                if (!string.IsNullOrEmpty(keyword.Synonyms))
                {
                    var synonyms = keyword.Synonyms.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    foreach (var synonym in synonyms)
                    {
                        var synonymText = keyword.IsCaseSensitive ? synonym.Trim() : synonym.Trim().ToLower();
                        bool synonymMatches = keyword.MatchType switch
                        {
                            "exact" => messageToCheck.Equals(synonymText),
                            "starts_with" => messageToCheck.StartsWith(synonymText),
                            "ends_with" => messageToCheck.EndsWith(synonymText),
                            _ => messageToCheck.Contains(synonymText)
                        };

                        if (synonymMatches)
                        {
                            var intentName = keyword.ChatbotIntent.Name;
                            if (!intentScores.ContainsKey(intentName))
                                intentScores[intentName] = 0;
                            intentScores[intentName] += keyword.Weight;
                        }
                    }
                }
            }

            // Return the intent with the highest score, or "general" if no matches
            return intentScores.Any()
                ? intentScores.OrderByDescending(kvp => kvp.Value).First().Key
                : "general";
        }

        private bool ContainsKeywords(string message, string[] keywords)
        {
            return keywords.Any(keyword => message.Contains(keyword));
        }

        private async Task<ChatbotResponse> GenerateResponse(string intent, string message, ChatbotContext? context)
        {
            // Always try to get response from database first
            var dbResponse = await GetDatabaseResponse(intent);
            if (dbResponse != null)
            {
                return dbResponse;
            }

            // If no database response found, create a dynamic response based on intent
            return intent switch
            {
                "services" => await GetDynamicServicesResponse(),
                "portfolio" => await GetDynamicPortfolioResponse(),
                "about" => await GetDynamicAboutResponse(),
                "contact" => await GetDynamicContactResponse(),
                "quote" => await GetDynamicQuoteResponse(),
                _ => await GetDynamicDefaultResponse()
            };
        }

        private async Task<ChatbotResponse?> GetDatabaseResponse(string intentName)
        {
            try
            {
                var intent = await _chatbotIntentRepository.GetAll()
                    .Include(i => i.Responses.Where(r => r.IsActive))
                    .ThenInclude(r => r.QuickActions.Where(qa => qa.IsActive))
                    .FirstOrDefaultAsync(i => i.Name == intentName && i.IsActive);

                if (intent == null || !intent.Responses.Any())
                {
                    return null;
                }

                // Get the first active response (you can add logic for multiple responses)
                var response = intent.Responses.OrderBy(r => r.DisplayOrder).First();

                // Process template variables if any
                var content = await ProcessTemplateVariables(response.Content, response.TemplateVariables);

                return new ChatbotResponse
                {
                    Content = content,
                    Intent = intentName,
                    QuickActions = response.QuickActions.OrderBy(qa => qa.DisplayOrder).Select(qa => new QuickAction
                    {
                        Label = qa.Label,
                        Value = qa.Value,
                        Icon = qa.IconClass
                    }).ToList()
                };
            }
            catch (Exception)
            {
                return null;
            }
        }

        private async Task<string> ProcessTemplateVariables(string content, string? templateVariables)
        {
            if (string.IsNullOrEmpty(templateVariables))
            {
                return content;
            }

            try
            {
                // Simple template variable processing
                // You can extend this to support more complex templating
                var processedContent = content;

                // Replace common variables with dynamic data
                if (content.Contains("{{service_count}}"))
                {
                    var serviceCount = await _serviceRepository.CountAsync(s => s.IsActive);
                    processedContent = processedContent.Replace("{{service_count}}", serviceCount.ToString());
                }

                if (content.Contains("{{project_count}}"))
                {
                    var projectCount = await _projectRepository.CountAsync(p => !p.IsDeleted);
                    processedContent = processedContent.Replace("{{project_count}}", projectCount.ToString());
                }

                if (content.Contains("{{team_count}}"))
                {
                    var teamCount = await _teamMemberRepository.CountAsync();
                    processedContent = processedContent.Replace("{{team_count}}", teamCount.ToString());
                }

                if (content.Contains("{{client_count}}"))
                {
                    var clientCount = await _projectRepository.GetAll()
                        .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.ClientName))
                        .Select(p => p.ClientName)
                        .Distinct()
                        .CountAsync();
                    processedContent = processedContent.Replace("{{client_count}}", clientCount.ToString());
                }

                return processedContent;
            }
            catch (Exception)
            {
                return content; // Return original content if template processing fails
            }
        }

        private async Task<ChatbotResponse> GetDynamicServicesResponse()
        {
            var services = await _serviceRepository.ListAsync(s => s.IsActive);
            var serviceList = services.OrderBy(s => s.DisplayOrder).ToList();

            if (!serviceList.Any())
            {
                return new ChatbotResponse
                {
                    Content = "<p>We offer comprehensive software development services. Please contact us to learn more about how we can help with your project.</p>",
                    Intent = "services",
                    QuickActions = new List<QuickAction>
                    {
                        new() { Label = "Contact Us", Value = "contact", Icon = "fas fa-envelope" },
                        new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" }
                    }
                };
            }

            var content = $"<p><strong>Our {serviceList.Count} Professional Services 🚀</strong></p>";

            foreach (var service in serviceList.Take(6))
            {
                var icon = GetServiceIcon(service.Name);
                var price = service.Price > 0 ? $" - Starting at ${service.Price:N0}" : "";
                content += $"<p><strong>{icon} {service.Name}</strong>{price}<br>{service.Description}</p>";
            }

            content += "<p>Which service interests you most?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "services",
                QuickActions = serviceList.Take(4).Select(s => new QuickAction
                {
                    Label = s.Name,
                    Value = $"service_{s.Id}",
                    Icon = "fas fa-code"
                }).ToList()
            };
        }

        private async Task<ChatbotResponse> GetDynamicPortfolioResponse()
        {
            var projects = await _projectRepository.GetAll()
                .Include(p => p.Service)
                .Include(p => p.Technologies)
                .Where(p => !p.IsDeleted)
                .OrderByDescending(p => p.CompletionDate)
                .Take(6)
                .ToListAsync();

            if (!projects.Any())
            {
                return new ChatbotResponse
                {
                    Content = "<p>We're building an impressive portfolio of projects. Contact us to discuss your project and see how we can help bring your vision to life!</p>",
                    Intent = "portfolio",
                    QuickActions = new List<QuickAction>
                    {
                        new() { Label = "Discuss Project", Value = "quote", Icon = "fas fa-comments" },
                        new() { Label = "Contact Us", Value = "contact", Icon = "fas fa-envelope" }
                    }
                };
            }

            var featuredProjects = projects.Where(p => p.IsFeatured).Take(3).ToList();
            var projectsToShow = featuredProjects.Any() ? featuredProjects : projects.Take(3).ToList();

            var content = $"<p><strong>Our Portfolio - {projects.Count()} Successful Projects 🎯</strong></p>";

            foreach (var project in projectsToShow)
            {
                var techList = string.Join(", ", project.Technologies?.Take(3).Select(t => t.Name) ?? new List<string>());
                var serviceInfo = project.Service != null ? $" ({project.Service.Name})" : "";
                var completionDate = project.CompletionDate != default(DateTime) ? project.CompletionDate.ToString("MMM yyyy") : "Recently";

                content += $"<p><strong>🚀 {project.Name}</strong>{serviceInfo}<br>" +
                          $"{project.Description}<br>" +
                          $"<em>Client: {project.ClientName} • Completed: {completionDate}</em>";

                if (!string.IsNullOrEmpty(techList))
                {
                    content += $"<br><em>Technologies: {techList}</em>";
                }

                content += "</p>";
            }

            content += "<p>Interested in a similar project or want to see more?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "portfolio",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "View All Projects", Value = "all_projects", Icon = "fas fa-external-link-alt" },
                    new() { Label = "Similar Project", Value = "quote", Icon = "fas fa-handshake" },
                    new() { Label = "Our Services", Value = "services", Icon = "fas fa-code" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" }
                }
            };
        }



        private async Task<ChatbotResponse> GetDynamicAboutResponse()
        {
            var teamCount = await _teamMemberRepository.CountAsync();
            var projectCount = await _projectRepository.CountAsync(p => !p.IsDeleted);
            var serviceCount = await _serviceRepository.CountAsync(s => s.IsActive);
            var clientCount = await _projectRepository.GetAll()
                .Where(p => !p.IsDeleted && !string.IsNullOrEmpty(p.ClientName))
                .Select(p => p.ClientName)
                .Distinct()
                .CountAsync();

            // Get company info from site settings
            var companyName = "TechnoloWay"; // Default fallback
            var companyDescription = "We're a passionate software development company dedicated to delivering innovative digital solutions.";

            try
            {
                var settings = await _serviceRepository.GetAll().Take(1).ToListAsync(); // Just to test DB connection
                // You can add site settings queries here if you have them
            }
            catch
            {
                // Fallback if database issues
            }

            var content = $"<p><strong>About {companyName} 🏢</strong></p>" +
                         $"<p>{companyDescription}</p>";

            if (teamCount > 0 || projectCount > 0 || clientCount > 0)
            {
                content += "<p><strong>Our Numbers:</strong><br>";
                if (teamCount > 0) content += $"👥 {teamCount}+ Expert Team Members<br>";
                if (projectCount > 0) content += $"🚀 {projectCount}+ Successful Projects<br>";
                if (clientCount > 0) content += $"🤝 {clientCount}+ Happy Clients<br>";
                if (serviceCount > 0) content += $"⚡ {serviceCount}+ Professional Services<br>";
                content += "</p>";
            }

            content += "<p><strong>What We Do:</strong><br>";

            // Get actual services from database
            var services = await _serviceRepository.ListAsync(s => s.IsActive);
            if (services.Any())
            {
                foreach (var service in services.Take(4))
                {
                    content += $"• {service.Name}<br>";
                }
            }
            else
            {
                content += "• Custom Software Development<br>" +
                          "• Web & Mobile Applications<br>" +
                          "• Cloud Solutions<br>" +
                          "• Technical Consulting<br>";
            }

            content += "</p><p>Ready to start your project or learn more about our team?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "about",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Our Services", Value = "services", Icon = "fas fa-code" },
                    new() { Label = "View Portfolio", Value = "portfolio", Icon = "fas fa-briefcase" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Contact Us", Value = "contact", Icon = "fas fa-envelope" }
                }
            };
        }



        private async Task<ChatbotResponse> GetDynamicQuoteResponse()
        {
            var services = await _serviceRepository.ListAsync(s => s.IsActive);
            var serviceActions = new List<QuickAction>();

            if (services.Any())
            {
                serviceActions = services.Take(3).Select(s => new QuickAction
                {
                    Label = s.Name,
                    Value = $"quote_{s.Id}",
                    Icon = "fas fa-code"
                }).ToList();
            }
            else
            {
                serviceActions = new List<QuickAction>
                {
                    new() { Label = "Web Development", Value = "quote_web", Icon = "fas fa-globe" },
                    new() { Label = "Mobile App", Value = "quote_mobile", Icon = "fas fa-mobile-alt" },
                    new() { Label = "Custom Software", Value = "quote_custom", Icon = "fas fa-cogs" }
                };
            }

            serviceActions.Add(new QuickAction { Label = "Talk to Expert", Value = "contact", Icon = "fas fa-user" });

            return new ChatbotResponse
            {
                Content = "<p><strong>Get Your Project Quote 💰</strong></p><p>I'd be happy to help you get a project estimate! What type of project are you planning?</p>",
                Intent = "quote",
                QuickActions = serviceActions
            };
        }

        private Task<ChatbotResponse> GetDynamicContactResponse()
        {
            return Task.FromResult(new ChatbotResponse
            {
                Content = "<p><strong>Let's Connect! 📞</strong></p><p>I'd be happy to connect you with our team!</p><p><strong>Contact Options:</strong></p><p>📞 <strong>Direct Call</strong> - Speak with our experts</p><p>📅 <strong>Schedule Meeting</strong> - Book a consultation</p><p>💬 <strong>Email Us</strong> - Get detailed information</p><p>🚀 <strong>Quick Quote</strong> - Get project estimate</p><p>What works best for you?</p>",
                Intent = "contact",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Schedule Call", Value = "schedule", Icon = "fas fa-calendar" },
                    new() { Label = "Email Us", Value = "email", Icon = "fas fa-envelope" },
                    new() { Label = "Continue Chat", Value = "services", Icon = "fas fa-comments" }
                }
            });
        }

        private async Task<ChatbotResponse> GetDynamicDefaultResponse()
        {
            var serviceCount = await _serviceRepository.CountAsync(s => s.IsActive);
            var projectCount = await _projectRepository.CountAsync(p => !p.IsDeleted);

            var content = "<p><strong>Hi there! 👋</strong></p>" +
                         "<p>I'm here to help you learn about our software development services.</p>";

            if (serviceCount > 0 || projectCount > 0)
            {
                content += "<p><strong>I can help you with:</strong></p>";
                if (serviceCount > 0) content += $"• Our {serviceCount} professional services<br>";
                if (projectCount > 0) content += $"• Portfolio of {projectCount}+ successful projects<br>";
                content += "• Getting project quotes<br>";
                content += "• Connecting with our team</p>";
            }
            else
            {
                content += "<p><strong>I can help you with:</strong></p>" +
                          "• Learning about our services<br>" +
                          "• Getting project quotes<br>" +
                          "• Viewing our portfolio<br>" +
                          "• Connecting with our team</p>";
            }

            content += "<p>What would you like to know more about?</p>";

            return new ChatbotResponse
            {
                Content = content,
                Intent = "general",
                QuickActions = new List<QuickAction>
                {
                    new() { Label = "Our Services", Value = "services", Icon = "fas fa-code" },
                    new() { Label = "View Portfolio", Value = "portfolio", Icon = "fas fa-briefcase" },
                    new() { Label = "Get Quote", Value = "quote", Icon = "fas fa-calculator" },
                    new() { Label = "Contact Us", Value = "contact", Icon = "fas fa-envelope" }
                }
            };
        }

        private string GetServiceIcon(string serviceName)
        {
            return serviceName.ToLower() switch
            {
                var name when name.Contains("web") => "🌐",
                var name when name.Contains("mobile") => "📱",
                var name when name.Contains("cloud") || name.Contains("devops") => "☁️",
                var name when name.Contains("consult") => "💼",
                var name when name.Contains("design") => "🎨",
                var name when name.Contains("data") => "📊",
                _ => "🚀"
            };
        }

        private string GetTechCategory(string techName)
        {
            return techName.ToLower() switch
            {
                var name when name.Contains("react") || name.Contains("vue") || name.Contains("angular") || name.Contains("javascript") || name.Contains("html") || name.Contains("css") => "Frontend",
                var name when name.Contains("node") || name.Contains("python") || name.Contains("java") || name.Contains("c#") || name.Contains(".net") || name.Contains("php") => "Backend",
                var name when name.Contains("aws") || name.Contains("azure") || name.Contains("docker") || name.Contains("kubernetes") || name.Contains("jenkins") => "Cloud & DevOps",
                var name when name.Contains("mysql") || name.Contains("postgresql") || name.Contains("mongodb") || name.Contains("redis") => "Database",
                _ => "Other"
            };
        }
    }

    // Request/Response models
    public class ChatbotMessageRequest
    {
        public string Message { get; set; } = string.Empty;
        public ChatbotContext? Context { get; set; }
    }

    public class ChatbotContext
    {
        public string? ProjectType { get; set; }
        public string? BudgetRange { get; set; }
        public string? Timeline { get; set; }
        public string? UserName { get; set; }
        public string? UserEmail { get; set; }
    }

    public class ChatbotContactRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string ContactMethod { get; set; } = "email";
        public string ContactTime { get; set; } = "morning";
        public string? ProjectType { get; set; }
        public string? BudgetRange { get; set; }
        public string? Timeline { get; set; }
        public string? Message { get; set; }
    }

    public class ChatbotResponse
    {
        public string Content { get; set; } = string.Empty;
        public string Intent { get; set; } = string.Empty;
        public List<QuickAction> QuickActions { get; set; } = new();
        public List<string> Suggestions { get; set; } = new();
    }

    public class QuickAction
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
    }
}
