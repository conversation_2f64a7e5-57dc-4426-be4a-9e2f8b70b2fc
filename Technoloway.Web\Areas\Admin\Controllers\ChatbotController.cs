using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;

namespace Technoloway.Web.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Roles = "SuperAdmin,ContentManager")]
    public class ChatbotController : Controller
    {
        private readonly IRepository<ChatbotIntent> _intentRepository;
        private readonly IRepository<ChatbotResponse> _responseRepository;
        private readonly IRepository<ChatbotKeyword> _keywordRepository;
        private readonly IRepository<ChatbotQuickAction> _quickActionRepository;

        public ChatbotController(
            IRepository<ChatbotIntent> intentRepository,
            IRepository<ChatbotResponse> responseRepository,
            IRepository<ChatbotKeyword> keywordRepository,
            IRepository<ChatbotQuickAction> quickActionRepository)
        {
            _intentRepository = intentRepository;
            _responseRepository = responseRepository;
            _keywordRepository = keywordRepository;
            _quickActionRepository = quickActionRepository;
        }

        // Intents Management
        public async Task<IActionResult> Intents()
        {
            var intents = await _intentRepository.GetAll()
                .Include(i => i.Keywords)
                .Include(i => i.Responses)
                .OrderBy(i => i.DisplayOrder)
                .ToListAsync();

            return View(intents);
        }

        public IActionResult CreateIntent()
        {
            return View(new ChatbotIntentViewModel());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateIntent(ChatbotIntentViewModel model)
        {
            if (ModelState.IsValid)
            {
                var intent = new ChatbotIntent
                {
                    Name = model.Name,
                    DisplayName = model.DisplayName,
                    Description = model.Description,
                    IsActive = model.IsActive,
                    DisplayOrder = model.DisplayOrder,
                    IconClass = model.IconClass,
                    CreatedAt = DateTime.UtcNow
                };

                await _intentRepository.AddAsync(intent);
                TempData["Success"] = "Chatbot intent created successfully!";
                return RedirectToAction(nameof(Intents));
            }

            return View(model);
        }

        public async Task<IActionResult> EditIntent(int id)
        {
            var intent = await _intentRepository.GetByIdAsync(id);
            if (intent == null)
            {
                return NotFound();
            }

            var model = new ChatbotIntentViewModel
            {
                Id = intent.Id,
                Name = intent.Name,
                DisplayName = intent.DisplayName,
                Description = intent.Description,
                IsActive = intent.IsActive,
                DisplayOrder = intent.DisplayOrder,
                IconClass = intent.IconClass
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditIntent(int id, ChatbotIntentViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var intent = await _intentRepository.GetByIdAsync(id);
                if (intent == null)
                {
                    return NotFound();
                }

                intent.Name = model.Name;
                intent.DisplayName = model.DisplayName;
                intent.Description = model.Description;
                intent.IsActive = model.IsActive;
                intent.DisplayOrder = model.DisplayOrder;
                intent.IconClass = model.IconClass;
                intent.UpdatedAt = DateTime.UtcNow;

                await _intentRepository.UpdateAsync(intent);
                TempData["Success"] = "Chatbot intent updated successfully!";
                return RedirectToAction(nameof(Intents));
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteIntent(int id)
        {
            var intent = await _intentRepository.GetByIdAsync(id);
            if (intent == null)
            {
                return NotFound();
            }

            await _intentRepository.DeleteAsync(intent);
            TempData["Success"] = "Chatbot intent deleted successfully!";
            return RedirectToAction(nameof(Intents));
        }

        // Responses Management
        public async Task<IActionResult> Responses(int? intentId)
        {
            IQueryable<Core.Entities.ChatbotResponse> query = _responseRepository.GetAll()
                .Include(r => r.ChatbotIntent)
                .Include(r => r.QuickActions);

            if (intentId.HasValue)
            {
                query = query.Where(r => r.ChatbotIntentId == intentId.Value);
                ViewBag.IntentId = intentId;
                var intent = await _intentRepository.GetByIdAsync(intentId.Value);
                ViewBag.IntentName = intent?.DisplayName;
            }
            else
            {
                ViewBag.IntentId = (int?)null;
            }

            var responses = await query.OrderBy(r => r.DisplayOrder).ToListAsync();
            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            return View(responses);
        }

        public async Task<IActionResult> CreateResponse(int? intentId)
        {
            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            var model = new ChatbotResponseViewModel();
            if (intentId.HasValue)
            {
                model.ChatbotIntentId = intentId.Value;
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateResponse(ChatbotResponseViewModel model)
        {
            if (ModelState.IsValid)
            {
                var response = new Core.Entities.ChatbotResponse
                {
                    Title = model.Title,
                    Content = model.Content,
                    ResponseType = model.ResponseType,
                    IsActive = model.IsActive,
                    DisplayOrder = model.DisplayOrder,
                    Conditions = model.Conditions ?? string.Empty,
                    TemplateVariables = model.TemplateVariables ?? string.Empty,
                    ChatbotIntentId = model.ChatbotIntentId,
                    CreatedAt = DateTime.UtcNow
                };

                await _responseRepository.AddAsync(response);
                TempData["Success"] = "Chatbot response created successfully!";
                return RedirectToAction(nameof(Responses), new { intentId = model.ChatbotIntentId });
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;
            return View(model);
        }

        public async Task<IActionResult> EditResponse(int id)
        {
            var response = await _responseRepository.GetByIdAsync(id);
            if (response == null)
            {
                return NotFound();
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            var model = new ChatbotResponseViewModel
            {
                Id = response.Id,
                Title = response.Title,
                Content = response.Content,
                ResponseType = response.ResponseType,
                IsActive = response.IsActive,
                DisplayOrder = response.DisplayOrder,
                Conditions = response.Conditions,
                TemplateVariables = response.TemplateVariables,
                ChatbotIntentId = response.ChatbotIntentId
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditResponse(int id, ChatbotResponseViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var response = await _responseRepository.GetByIdAsync(id);
                if (response == null)
                {
                    return NotFound();
                }

                response.Title = model.Title;
                response.Content = model.Content;
                response.ResponseType = model.ResponseType;
                response.IsActive = model.IsActive;
                response.DisplayOrder = model.DisplayOrder;
                response.Conditions = model.Conditions ?? string.Empty;
                response.TemplateVariables = model.TemplateVariables ?? string.Empty;
                response.ChatbotIntentId = model.ChatbotIntentId;
                response.UpdatedAt = DateTime.UtcNow;

                await _responseRepository.UpdateAsync(response);
                TempData["Success"] = "Chatbot response updated successfully!";
                return RedirectToAction(nameof(Responses), new { intentId = model.ChatbotIntentId });
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteResponse(int id)
        {
            var response = await _responseRepository.GetByIdAsync(id);
            if (response == null)
            {
                return NotFound();
            }

            var intentId = response.ChatbotIntentId;
            await _responseRepository.DeleteAsync(response);
            TempData["Success"] = "Chatbot response deleted successfully!";
            return RedirectToAction(nameof(Responses), new { intentId });
        }

        // Keywords Management
        public async Task<IActionResult> Keywords(int? intentId)
        {
            IQueryable<ChatbotKeyword> query = _keywordRepository.GetAll().Include(k => k.ChatbotIntent);

            if (intentId.HasValue)
            {
                query = query.Where(k => k.ChatbotIntentId == intentId.Value);
                ViewBag.IntentId = intentId;
                var intent = await _intentRepository.GetByIdAsync(intentId.Value);
                ViewBag.IntentName = intent?.DisplayName;
            }
            else
            {
                ViewBag.IntentId = (int?)null;
            }

            var keywords = await query.OrderBy(k => k.Weight).ThenBy(k => k.Keyword).ToListAsync();
            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            return View(keywords);
        }

        public async Task<IActionResult> CreateKeyword(int? intentId)
        {
            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            var model = new ChatbotKeywordViewModel();
            if (intentId.HasValue)
            {
                model.ChatbotIntentId = intentId.Value;
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateKeyword(ChatbotKeywordViewModel model)
        {
            if (ModelState.IsValid)
            {
                var keyword = new ChatbotKeyword
                {
                    Keyword = model.Keyword,
                    Synonyms = model.Synonyms ?? string.Empty,
                    Weight = model.Weight,
                    IsActive = model.IsActive,
                    MatchType = model.MatchType,
                    IsCaseSensitive = model.IsCaseSensitive,
                    ChatbotIntentId = model.ChatbotIntentId,
                    CreatedAt = DateTime.UtcNow
                };

                await _keywordRepository.AddAsync(keyword);
                TempData["Success"] = "Chatbot keyword created successfully!";
                return RedirectToAction(nameof(Keywords), new { intentId = model.ChatbotIntentId });
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;
            return View(model);
        }

        public async Task<IActionResult> EditKeyword(int id)
        {
            var keyword = await _keywordRepository.GetByIdAsync(id);
            if (keyword == null)
            {
                return NotFound();
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;

            var model = new ChatbotKeywordViewModel
            {
                Id = keyword.Id,
                Keyword = keyword.Keyword,
                Synonyms = keyword.Synonyms,
                Weight = keyword.Weight,
                IsActive = keyword.IsActive,
                MatchType = keyword.MatchType,
                IsCaseSensitive = keyword.IsCaseSensitive,
                ChatbotIntentId = keyword.ChatbotIntentId
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditKeyword(int id, ChatbotKeywordViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var keyword = await _keywordRepository.GetByIdAsync(id);
                if (keyword == null)
                {
                    return NotFound();
                }

                keyword.Keyword = model.Keyword;
                keyword.Synonyms = model.Synonyms ?? string.Empty;
                keyword.Weight = model.Weight;
                keyword.IsActive = model.IsActive;
                keyword.MatchType = model.MatchType;
                keyword.IsCaseSensitive = model.IsCaseSensitive;
                keyword.ChatbotIntentId = model.ChatbotIntentId;
                keyword.UpdatedAt = DateTime.UtcNow;

                await _keywordRepository.UpdateAsync(keyword);
                TempData["Success"] = "Chatbot keyword updated successfully!";
                return RedirectToAction(nameof(Keywords), new { intentId = model.ChatbotIntentId });
            }

            var intents = await _intentRepository.ListAsync(i => i.IsActive);
            ViewBag.Intents = intents;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteKeyword(int id)
        {
            var keyword = await _keywordRepository.GetByIdAsync(id);
            if (keyword == null)
            {
                return NotFound();
            }

            var intentId = keyword.ChatbotIntentId;
            await _keywordRepository.DeleteAsync(keyword);
            TempData["Success"] = "Chatbot keyword deleted successfully!";
            return RedirectToAction(nameof(Keywords), new { intentId });
        }
    }
}
