using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class TechnologiesController : Controller
{
    private readonly IRepository<Technology> _technologyRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public TechnologiesController(
        IRepository<Technology> technologyRepository,
        IRepository<Project> projectRepository,
        IHeroSectionRepository heroSectionRepository)
    {
        _technologyRepository = technologyRepository;
        _projectRepository = projectRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index()
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Technologies");
        var technologies = await _technologyRepository.GetAll()
            .Where(t => !t.IsDeleted)
            .OrderBy(t => t.DisplayOrder)
            .ToListAsync();

        ViewBag.HeroSection = heroSection;
        ViewBag.PageName = "Technologies";
        return View(technologies);
    }

    public async Task<IActionResult> Details(int id)
    {
        var technology = await _technologyRepository.GetByIdAsync(id);

        if (technology == null || technology.IsDeleted)
        {
            return NotFound();
        }

        // In a real application, we would use a many-to-many relationship
        // For this demo, we'll just get some projects to display
        var projects = await _projectRepository.GetAll()
            .Where(p => !p.IsDeleted)
            .OrderByDescending(p => p.CompletionDate)
            .Take(6)
            .ToListAsync();

        var viewModel = new TechnologyDetailsViewModel
        {
            Technology = technology,
            RelatedProjects = projects
        };

        return View(viewModel);
    }
}
