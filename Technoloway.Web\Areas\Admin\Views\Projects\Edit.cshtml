@model Technoloway.Web.Areas.Admin.Models.ProjectViewModel

@{
    ViewData["Title"] = "Edit Project";
    Layout = "_AdminLayout";
    var technologies = ViewBag.Technologies as IEnumerable<Technoloway.Core.Entities.Technology>;
    var selectedTechnologies = ViewBag.SelectedTechnologies as int[] ?? Array.Empty<int>();
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Project</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Project Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" method="post" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Name" class="control-label">Project Name</label>
                        <input asp-for="Name" class="form-control" required />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Description" class="control-label">Description</label>
                        <textarea asp-for="Description" class="form-control" rows="5" required></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ClientId" class="control-label">Client</label>
                        <select asp-for="ClientId" asp-items="ViewBag.Clients" class="form-control">
                            <option value="">-- Select Client --</option>
                        </select>
                        <span asp-validation-for="ClientId" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ServiceId" class="control-label">Service</label>
                        <select asp-for="ServiceId" asp-items="ViewBag.Services" class="form-control" required>
                            <option value="">-- Select Service --</option>
                        </select>
                        <span asp-validation-for="ServiceId" class="text-danger"></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="ImageFile" class="control-label">Project Image</label>
                        <input asp-for="ImageFile" type="file" class="form-control" accept="image/*" id="projectImageInput" />
                        <span asp-validation-for="ImageFile" class="text-danger"></span>
                        <small class="form-text text-muted">Upload an image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.</small>

                        <!-- Current Image Preview -->
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <div id="currentImage" class="mt-3">
                                <label class="form-label">Current Project Image:</label>
                                <div>
                                    <img src="@Model.ImageUrl" alt="Current project image" class="img-thumbnail" style="max-width: 300px; max-height: 200px;" />
                                </div>
                            </div>
                        }

                        <!-- New Image Preview -->
                        <div id="imagePreview" class="mt-3" style="display: none;">
                            <label class="form-label">New Project Image Preview:</label>
                            <div>
                                <img id="previewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 300px; max-height: 200px;" />
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="clearImagePreview()">Remove New Image</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ProjectUrl" class="control-label">Project URL</label>
                        <input asp-for="ProjectUrl" class="form-control" />
                        <span asp-validation-for="ProjectUrl" class="text-danger"></span>
                        <small class="form-text text-muted">URL to the live project (if applicable)</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="CompletionDate" class="control-label">Completion Date</label>
                        <input asp-for="CompletionDate" type="date" class="form-control" value="@Model.CompletionDate.ToString("yyyy-MM-dd")" required />
                        <span asp-validation-for="CompletionDate" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="IsFeatured" class="custom-control-input" />
                            <label asp-for="IsFeatured" class="custom-control-label">Featured Project</label>
                        </div>
                        <small class="form-text text-muted">Featured projects are highlighted on the homepage</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="DisplayOrder" class="control-label">Display Order</label>
                        <input asp-for="DisplayOrder" type="number" class="form-control" />
                        <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        <small class="form-text text-muted">Lower numbers are displayed first</small>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <label class="control-label">Technologies</label>
                <div class="row">
                    @if (technologies != null)
                    {
                        foreach (var tech in technologies)
                        {
                            <div class="col-md-3 mb-2">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="selectedTechnologies" value="@tech.Id" id="<EMAIL>" class="custom-control-input"
                                           @(selectedTechnologies.Contains(tech.Id) ? "checked" : "") />
                                    <label for="<EMAIL>" class="custom-control-label">@tech.Name</label>
                                </div>
                            </div>
                        }
                    }
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Save</button>
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Image preview functionality
        document.getElementById('projectImageInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImg').src = e.target.result;
                    document.getElementById('imagePreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        function clearImagePreview() {
            document.getElementById('projectImageInput').value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('previewImg').src = '';
        }
    </script>
}
