using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data
{
    public static class ChatbotSeeder
    {
        public static async Task SeedChatbotData(ApplicationDbContext context)
        {
            // Check if chatbot data already exists
            if (await context.ChatbotIntents.AnyAsync())
            {
                return; // Data already seeded
            }

            // Create default intents
            var intents = new List<ChatbotIntent>
            {
                new ChatbotIntent
                {
                    Name = "services",
                    DisplayName = "Our Services",
                    Description = "Information about our software development services",
                    IsActive = true,
                    DisplayOrder = 1,
                    IconClass = "fas fa-code",
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotIntent
                {
                    Name = "portfolio",
                    DisplayName = "Portfolio & Projects",
                    Description = "Showcase of our completed projects and work",
                    IsActive = true,
                    DisplayOrder = 2,
                    IconClass = "fas fa-briefcase",
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotIntent
                {
                    Name = "about",
                    DisplayName = "About Company",
                    Description = "Information about our company, team, and values",
                    IsActive = true,
                    DisplayOrder = 3,
                    IconClass = "fas fa-building",
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotIntent
                {
                    Name = "contact",
                    DisplayName = "Contact & Support",
                    Description = "Contact information and support options",
                    IsActive = true,
                    DisplayOrder = 4,
                    IconClass = "fas fa-envelope",
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotIntent
                {
                    Name = "quote",
                    DisplayName = "Get Quote",
                    Description = "Project quote and pricing information",
                    IsActive = true,
                    DisplayOrder = 5,
                    IconClass = "fas fa-calculator",
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotIntent
                {
                    Name = "general",
                    DisplayName = "General Help",
                    Description = "Default response for unmatched queries",
                    IsActive = true,
                    DisplayOrder = 99,
                    IconClass = "fas fa-question-circle",
                    CreatedAt = DateTime.UtcNow
                }
            };

            await context.ChatbotIntents.AddRangeAsync(intents);
            await context.SaveChangesAsync();

            // Create keywords for each intent
            var keywords = new List<ChatbotKeyword>
            {
                // Services keywords
                new ChatbotKeyword { Keyword = "service", Synonyms = "services,offering,offerings", Weight = 5, ChatbotIntentId = intents[0].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "development", Synonyms = "develop,programming,coding", Weight = 4, ChatbotIntentId = intents[0].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "what do you do", Synonyms = "", Weight = 6, ChatbotIntentId = intents[0].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },

                // Portfolio keywords
                new ChatbotKeyword { Keyword = "portfolio", Synonyms = "work,projects,examples", Weight = 5, ChatbotIntentId = intents[1].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "project", Synonyms = "projects", Weight = 4, ChatbotIntentId = intents[1].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "showcase", Synonyms = "gallery,samples", Weight = 3, ChatbotIntentId = intents[1].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },

                // About keywords
                new ChatbotKeyword { Keyword = "about", Synonyms = "company,team,who", Weight = 5, ChatbotIntentId = intents[2].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "who are you", Synonyms = "", Weight = 6, ChatbotIntentId = intents[2].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "team", Synonyms = "staff,employees", Weight = 4, ChatbotIntentId = intents[2].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },

                // Contact keywords
                new ChatbotKeyword { Keyword = "contact", Synonyms = "reach,call,email", Weight = 5, ChatbotIntentId = intents[3].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "support", Synonyms = "help,assistance", Weight = 4, ChatbotIntentId = intents[3].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "talk to human", Synonyms = "speak to person,human agent", Weight = 6, ChatbotIntentId = intents[3].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },

                // Quote keywords
                new ChatbotKeyword { Keyword = "quote", Synonyms = "price,cost,estimate", Weight = 5, ChatbotIntentId = intents[4].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "pricing", Synonyms = "budget,rates", Weight = 4, ChatbotIntentId = intents[4].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow },
                new ChatbotKeyword { Keyword = "how much", Synonyms = "", Weight = 5, ChatbotIntentId = intents[4].Id, IsActive = true, MatchType = "contains", CreatedAt = DateTime.UtcNow }
            };

            await context.ChatbotKeywords.AddRangeAsync(keywords);
            await context.SaveChangesAsync();

            // Create responses for each intent
            var responses = new List<ChatbotResponse>
            {
                new ChatbotResponse
                {
                    Title = "Services Response",
                    Content = "<p><strong>🚀 Our {{service_count}} Professional Services</strong></p><p>We specialize in: <strong>{{service_list}}</strong></p><p><strong>Our Core Offerings:</strong></p><p>• <strong>Web Development</strong> - Modern, responsive websites and web applications<br>• <strong>Mobile Apps</strong> - iOS and Android native and cross-platform apps<br>• <strong>Cloud Solutions</strong> - Scalable cloud-native applications<br>• <strong>API Development</strong> - RESTful APIs and microservices<br>• <strong>DevOps & Deployment</strong> - CI/CD pipelines and infrastructure</p><p>Each service is delivered by our expert team with proven results. Which service interests you most?</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    TemplateVariables = "{\"service_count\": \"dynamic\", \"service_list\": \"dynamic\"}",
                    ChatbotIntentId = intents[0].Id,
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotResponse
                {
                    Title = "Portfolio Response",
                    Content = "<p><strong>🎯 Our Portfolio & Recent Work</strong></p><p>We've successfully completed <strong>{{project_count}}</strong> projects for <strong>{{client_count}}</strong> satisfied clients.</p><p><strong>Recent Projects:</strong> {{recent_projects}}</p><p><strong>Our Expertise Includes:</strong></p><p>• E-commerce platforms with advanced features<br>• Enterprise web applications<br>• Mobile apps with 100K+ downloads<br>• Cloud migration projects<br>• Custom CRM and ERP systems</p><p>Would you like to see specific examples or discuss a similar project for your business?</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    TemplateVariables = "{\"project_count\": \"dynamic\", \"client_count\": \"dynamic\", \"recent_projects\": \"dynamic\"}",
                    ChatbotIntentId = intents[1].Id,
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotResponse
                {
                    Title = "About Company Response",
                    Content = "<p><strong>🏢 About TechnoloWay</strong></p><p>We're a passionate software development company founded in 2018, dedicated to delivering innovative digital solutions that drive business growth.</p><p><strong>Our Team:</strong><br>👥 {{team_count}}+ Expert Developers<br>🌍 Remote-friendly, Global Talent<br>🎓 Certified in latest technologies</p><p><strong>Our Approach:</strong><br>• Agile development methodology<br>• Client-focused solutions<br>• Quality-first mindset<br>• 24/7 support available</p><p>Ready to transform your ideas into reality?</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    TemplateVariables = "{\"team_count\": \"dynamic\"}",
                    ChatbotIntentId = intents[2].Id,
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotResponse
                {
                    Title = "Contact Response",
                    Content = "<p><strong>📞 Get In Touch With Us</strong></p><p>We'd love to hear from you! Here are the best ways to reach our team:</p><p><strong>Contact Options:</strong><br>📧 <strong>Email:</strong> <EMAIL><br>📱 <strong>Phone:</strong> +****************<br>💬 <strong>Live Chat:</strong> Available Mon-Fri, 9 AM - 6 PM PST<br>📅 <strong>Schedule a Call:</strong> Book a free consultation</p><p><strong>Response Times:</strong><br>• Email: Within 2 hours<br>• Phone: Immediate during business hours<br>• Project inquiries: Same day response</p><p>What's the best way for us to connect with you?</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    ChatbotIntentId = intents[3].Id,
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotResponse
                {
                    Title = "Quote Response",
                    Content = "<p><strong>💰 Get Your Project Quote</strong></p><p>I'd be happy to help you get an accurate project estimate! Let me gather some information:</p><p><strong>What type of project are you planning?</strong></p><p>• New website or web application<br>• Mobile app (iOS/Android)<br>• System integration or upgrade<br>• Custom software solution<br>• E-commerce platform</p><p><strong>Quick Estimate Ranges:</strong><br>🌐 Basic Website: $2,000 - $5,000<br>🚀 Web Application: $5,000 - $25,000<br>📱 Mobile App: $10,000 - $50,000<br>🏢 Enterprise Solution: $25,000+</p><p>For a detailed quote, let's discuss your specific requirements!</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    ChatbotIntentId = intents[4].Id,
                    CreatedAt = DateTime.UtcNow
                },
                new ChatbotResponse
                {
                    Title = "General Help Response",
                    Content = "<p><strong>👋 How Can I Help You Today?</strong></p><p>I'm here to assist you with information about TechnoloWay. I can help you with:</p><p>🔹 <strong>Our Services</strong> - Learn about our development offerings<br>🔹 <strong>Portfolio</strong> - View our completed projects and case studies<br>🔹 <strong>Company Info</strong> - About our team and approach<br>🔹 <strong>Get Quote</strong> - Pricing and project estimates<br>🔹 <strong>Contact</strong> - Ways to reach our team</p><p>Just ask me anything, or click one of the options below to get started!</p>",
                    ResponseType = "html",
                    IsActive = true,
                    DisplayOrder = 1,
                    ChatbotIntentId = intents[5].Id,
                    CreatedAt = DateTime.UtcNow
                }
            };

            await context.ChatbotResponses.AddRangeAsync(responses);
            await context.SaveChangesAsync();

            // Create quick actions for responses
            var quickActions = new List<ChatbotQuickAction>
            {
                // Services quick actions
                new ChatbotQuickAction { Label = "Web Development", Value = "web_dev", IconClass = "fas fa-globe", ActionType = "intent", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[0].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Mobile Apps", Value = "mobile_dev", IconClass = "fas fa-mobile-alt", ActionType = "intent", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[0].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Get Quote", Value = "quote", IconClass = "fas fa-calculator", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[0].Id, CreatedAt = DateTime.UtcNow },

                // Portfolio quick actions
                new ChatbotQuickAction { Label = "View Projects", Value = "view_portfolio", IconClass = "fas fa-external-link-alt", ActionType = "url", Url = "/projects", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[1].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Similar Project", Value = "similar_project", IconClass = "fas fa-handshake", ActionType = "intent", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[1].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Get Quote", Value = "quote", IconClass = "fas fa-calculator", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[1].Id, CreatedAt = DateTime.UtcNow },

                // About quick actions
                new ChatbotQuickAction { Label = "Our Team", Value = "team", IconClass = "fas fa-users", ActionType = "url", Url = "/about#team", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[2].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Our Process", Value = "process", IconClass = "fas fa-cogs", ActionType = "url", Url = "/about#process", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[2].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Start Project", Value = "quote", IconClass = "fas fa-rocket", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[2].Id, CreatedAt = DateTime.UtcNow },

                // Contact quick actions
                new ChatbotQuickAction { Label = "Call Now", Value = "call", IconClass = "fas fa-phone", ActionType = "url", Url = "tel:+15551234567", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[3].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Send Email", Value = "email", IconClass = "fas fa-envelope", ActionType = "url", Url = "mailto:<EMAIL>", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[3].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Schedule Call", Value = "schedule", IconClass = "fas fa-calendar", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[3].Id, CreatedAt = DateTime.UtcNow },

                // Quote quick actions
                new ChatbotQuickAction { Label = "Website Quote", Value = "quote_website", IconClass = "fas fa-globe", ActionType = "intent", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[4].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "App Quote", Value = "quote_app", IconClass = "fas fa-mobile-alt", ActionType = "intent", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[4].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Talk to Expert", Value = "contact", IconClass = "fas fa-user", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[4].Id, CreatedAt = DateTime.UtcNow },

                // General quick actions
                new ChatbotQuickAction { Label = "Our Services", Value = "services", IconClass = "fas fa-code", ActionType = "intent", IsActive = true, DisplayOrder = 1, ChatbotResponseId = responses[5].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "View Portfolio", Value = "portfolio", IconClass = "fas fa-briefcase", ActionType = "intent", IsActive = true, DisplayOrder = 2, ChatbotResponseId = responses[5].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Get Quote", Value = "quote", IconClass = "fas fa-calculator", ActionType = "intent", IsActive = true, DisplayOrder = 3, ChatbotResponseId = responses[5].Id, CreatedAt = DateTime.UtcNow },
                new ChatbotQuickAction { Label = "Contact Us", Value = "contact", IconClass = "fas fa-envelope", ActionType = "intent", IsActive = true, DisplayOrder = 4, ChatbotResponseId = responses[5].Id, CreatedAt = DateTime.UtcNow }
            };

            await context.ChatbotQuickActions.AddRangeAsync(quickActions);
            await context.SaveChangesAsync();
        }
    }
}
