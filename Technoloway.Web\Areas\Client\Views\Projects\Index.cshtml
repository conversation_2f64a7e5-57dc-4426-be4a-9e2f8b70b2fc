@model IEnumerable<Technoloway.Core.Entities.Project>
@{
    ViewData["Title"] = "My Projects";
}

<div class="container-fluid p-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">My Projects</h1>
                    <p class="text-muted">Track your project progress and collaborate with our team</p>
                </div>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">All Projects</a></li>
                            <li><a class="dropdown-item" href="#">In Progress</a></li>
                            <li><a class="dropdown-item" href="#">Completed</a></li>
                            <li><a class="dropdown-item" href="#">On Hold</a></li>
                        </ul>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-sort me-1"></i> Sort
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Newest First</a></li>
                            <li><a class="dropdown-item" href="#">Oldest First</a></li>
                            <li><a class="dropdown-item" href="#">Name A-Z</a></li>
                            <li><a class="dropdown-item" href="#">Name Z-A</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Grid -->
    @if (Model != null && Model.Any())
    {
        <div class="row">
            @foreach (var project in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card project-card h-100 fade-in">
                        <!-- Project Image/Icon -->
                        <div class="card-img-top position-relative" style="height: 200px; background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);">
                            @if (!string.IsNullOrEmpty(project.ImageUrl))
                            {
                                <img src="@project.ImageUrl" alt="@project.Name" class="w-100 h-100" style="object-fit: cover;">
                            }
                            else
                            {
                                <div class="d-flex align-items-center justify-content-center h-100">
                                    <i class="fas fa-project-diagram fa-3x text-white"></i>
                                </div>
                            }
                            
                            <!-- Status Badge -->
                            <div class="position-absolute top-0 end-0 m-3">
                                <span class="status-indicator in-progress">In Progress</span>
                            </div>
                            
                            <!-- Progress Overlay -->
                            <div class="position-absolute bottom-0 start-0 end-0 p-3 bg-dark bg-opacity-50">
                                <div class="d-flex justify-content-between align-items-center text-white">
                                    <small>Progress</small>
                                    <small>75%</small>
                                </div>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-success" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title mb-0">@project.Name</h5>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" asp-action="Details" asp-route-id="@project.Id">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" asp-action="Messages" asp-route-id="@project.Id">
                                            <i class="fas fa-comments me-2"></i>Messages
                                        </a></li>
                                        <li><a class="dropdown-item" asp-action="Documents" asp-route-id="@project.Id">
                                            <i class="fas fa-folder me-2"></i>Documents
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                            
                            <p class="card-text text-muted small mb-3">
                                @project.Description.Substring(0, Math.Min(100, project.Description.Length))@(project.Description.Length > 100 ? "..." : "")
                            </p>
                            
                            <!-- Project Meta Info -->
                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-tag text-primary me-2"></i>
                                        <small class="text-muted">@(project.Service?.Name ?? "N/A")</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar text-success me-2"></i>
                                        <small class="text-muted">@project.CreatedAt.ToString("MMM dd, yyyy")</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Technologies -->
                            @if (project.Technologies != null && project.Technologies.Any())
                            {
                                <div class="mb-3">
                                    <small class="text-muted d-block mb-1">Technologies:</small>
                                    <div class="d-flex flex-wrap gap-1">
                                        @foreach (var tech in project.Technologies.Take(3))
                                        {
                                            <span class="badge bg-light text-dark">@tech.Name</span>
                                        }
                                        @if (project.Technologies.Count() > 3)
                                        {
                                            <span class="badge bg-secondary">+@(project.Technologies.Count() - 3) more</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                        
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-flex gap-2">
                                <a asp-action="Details" asp-route-id="@project.Id" class="btn btn-primary btn-sm flex-fill">
                                    <i class="fas fa-eye me-1"></i> View Details
                                </a>
                                <a asp-action="Messages" asp-route-id="@project.Id" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-comment"></i>
                                </a>
                                <a asp-action="Documents" asp-route-id="@project.Id" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-folder"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <!-- Empty State -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-project-diagram fa-4x text-muted mb-4"></i>
                            <h4 class="text-muted mb-3">No Projects Yet</h4>
                            <p class="text-muted mb-4">
                                You don't have any projects yet. Once we start working on your projects, they'll appear here.
                            </p>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="@Url.Action("Contact", "Home", new { area = "" })" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Start a New Project
                                </a>
                                <a href="@Url.Action("Index", "Messages")" class="btn btn-outline-primary">
                                    <i class="fas fa-comment me-2"></i>Contact Us
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
.project-card {
    transition: var(--transition);
    border: none;
    box-shadow: var(--shadow-md);
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.empty-state {
    max-width: 400px;
    margin: 0 auto;
}

.card-img-top {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add staggered animation to project cards
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
</script>
