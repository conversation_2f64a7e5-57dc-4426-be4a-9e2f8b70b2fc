# Chatbot Integration Guide & Training Tips

## Quick Integration Methods

### 1. HTML/CSS/JavaScript Integration

**Simple Embed (Copy & Paste):**
```html
<!-- Add to your HTML head -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="path/to/chatbot-styles.css">

<!-- Add before closing body tag -->
<script src="path/to/chatbot-logic.js"></script>
```

**CDN Integration:**
```html
<!-- Hosted version -->
<script src="https://your-domain.com/chatbot/widget.js"></script>
<script>
  TechnoloWayChatbot.init({
    apiKey: 'your-api-key',
    theme: 'default',
    position: 'bottom-right'
  });
</script>
```

### 2. React Integration

```jsx
import ChatbotComponent from './components/ChatbotComponent';

function App() {
  return (
    <div className="App">
      {/* Your app content */}
      <ChatbotComponent />
    </div>
  );
}
```

### 3. Vue.js Integration

```vue
<template>
  <div id="app">
    <!-- Your app content -->
    <Chatbot />
  </div>
</template>

<script>
import Chatbot from './components/Chatbot.vue'

export default {
  components: {
    Chatbot
  }
}
</script>
```

### 4. WordPress Integration

```php
// functions.php
function add_technoloway_chatbot() {
    wp_enqueue_style('chatbot-styles', get_template_directory_uri() . '/assets/chatbot-styles.css');
    wp_enqueue_script('chatbot-logic', get_template_directory_uri() . '/assets/chatbot-logic.js', array(), '1.0.0', true);
}
add_action('wp_enqueue_scripts', 'add_technoloway_chatbot');
```

## Backend API Integration

### Node.js/Express Backend

```javascript
// chatbot-routes.js
const express = require('express');
const router = express.Router();
const OpenAI = require('openai');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

// Process chatbot messages
router.post('/message', async (req, res) => {
  try {
    const { message, context } = req.body;
    
    // Custom logic for intent detection
    const intent = detectIntent(message);
    
    if (intent === 'quote_request') {
      return res.json(handleQuoteRequest(message, context));
    }
    
    if (intent === 'human_handoff') {
      return res.json(handleHumanHandoff(context));
    }
    
    // Fallback to OpenAI
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are Alex, a helpful assistant for TechnoloWay, a software development company. 
          
          Company Info:
          - Services: Web development, mobile apps, DevOps, consulting
          - Founded: 2018
          - Team: 25+ developers
          - Location: San Francisco, CA
          
          Always be professional, helpful, and try to qualify leads by asking about:
          - Project type and scope
          - Budget range
          - Timeline
          - Technical requirements
          
          If users want detailed quotes or to speak with humans, collect their contact info.`
        },
        {
          role: "user",
          content: message
        }
      ],
      max_tokens: 300,
      temperature: 0.7
    });

    res.json({
      response: completion.choices[0].message.content,
      intent: intent,
      suggestions: getSuggestions(intent)
    });

  } catch (error) {
    console.error('Chatbot error:', error);
    res.status(500).json({ error: 'Failed to process message' });
  }
});

// Lead capture
router.post('/leads', async (req, res) => {
  try {
    const leadData = req.body;
    
    // Save to database
    await saveLead(leadData);
    
    // Send notification to sales team
    await notifySalesTeam(leadData);
    
    // Send confirmation email
    await sendConfirmationEmail(leadData.email);
    
    res.json({ success: true, message: 'Lead captured successfully' });
  } catch (error) {
    console.error('Lead capture error:', error);
    res.status(500).json({ error: 'Failed to capture lead' });
  }
});

function detectIntent(message) {
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('quote') || lowerMessage.includes('price') || lowerMessage.includes('cost')) {
    return 'quote_request';
  }
  
  if (lowerMessage.includes('human') || lowerMessage.includes('person') || lowerMessage.includes('agent')) {
    return 'human_handoff';
  }
  
  if (lowerMessage.includes('service') || lowerMessage.includes('what do you do')) {
    return 'services_inquiry';
  }
  
  return 'general';
}

module.exports = router;
```

### Python/Flask Backend

```python
# chatbot_api.py
from flask import Flask, request, jsonify
import openai
import os
from datetime import datetime

app = Flask(__name__)
openai.api_key = os.getenv('OPENAI_API_KEY')

@app.route('/api/chatbot/message', methods=['POST'])
def process_message():
    try:
        data = request.json
        message = data.get('message')
        context = data.get('context', {})
        
        # Intent detection
        intent = detect_intent(message)
        
        if intent == 'quote_request':
            return jsonify(handle_quote_request(message, context))
        
        # OpenAI integration
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {
                    "role": "system",
                    "content": """You are Alex, TechnoloWay's virtual assistant.
                    
                    Company: TechnoloWay - Software Development Company
                    Services: Web development, mobile apps, DevOps, consulting
                    Founded: 2018, Team: 25+ developers, Location: San Francisco
                    
                    Your goals:
                    1. Answer questions about services
                    2. Qualify leads (project type, budget, timeline)
                    3. Collect contact info for detailed quotes
                    4. Be professional and helpful
                    
                    Keep responses concise and actionable."""
                },
                {"role": "user", "content": message}
            ],
            max_tokens=300,
            temperature=0.7
        )
        
        return jsonify({
            'response': response.choices[0].message.content,
            'intent': intent,
            'suggestions': get_suggestions(intent)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def detect_intent(message):
    message_lower = message.lower()
    
    if any(word in message_lower for word in ['quote', 'price', 'cost', 'budget']):
        return 'quote_request'
    elif any(word in message_lower for word in ['human', 'person', 'agent', 'talk']):
        return 'human_handoff'
    elif any(word in message_lower for word in ['service', 'services', 'what do you do']):
        return 'services_inquiry'
    else:
        return 'general'

if __name__ == '__main__':
    app.run(debug=True)
```

## Third-Party Service Integrations

### 1. OpenAI GPT Integration

```javascript
// Enhanced OpenAI integration with custom training
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY
});

async function getAIResponse(message, context) {
  const systemPrompt = `
    You are Alex, a virtual assistant for TechnoloWay software development company.
    
    COMPANY INFORMATION:
    - Name: TechnoloWay
    - Founded: 2018
    - Team: 25+ experienced developers
    - Location: San Francisco, CA (Remote-friendly)
    - Specialties: Web development, mobile apps, DevOps, consulting
    
    SERVICES & PRICING:
    - Web Development: $5,000 - $50,000+ (4-16 weeks)
    - Mobile Apps: $15,000 - $100,000+ (8-24 weeks)
    - DevOps/Cloud: $10,000 - $75,000+ (2-12 weeks)
    - Consulting: $150-250/hour
    
    CONVERSATION GOALS:
    1. Qualify leads by asking about:
       - Project type and scope
       - Budget range ($10K, $10K-50K, $50K-100K, $100K+)
       - Timeline (ASAP, 1-2 months, 3-6 months, 6+ months)
       - Technical requirements
    
    2. Provide helpful information about:
       - Our services and expertise
       - Portfolio examples
       - Development process
       - Team capabilities
    
    3. Guide users toward:
       - Scheduling consultations
       - Requesting detailed quotes
       - Speaking with human agents
    
    RESPONSE STYLE:
    - Professional but friendly
    - Concise and actionable
    - Use emojis sparingly
    - Always offer next steps
    - Ask qualifying questions
    
    CONVERSATION CONTEXT:
    ${JSON.stringify(context)}
  `;

  try {
    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: message }
      ],
      max_tokens: 400,
      temperature: 0.7,
      presence_penalty: 0.1,
      frequency_penalty: 0.1
    });

    return completion.choices[0].message.content;
  } catch (error) {
    console.error('OpenAI API error:', error);
    return "I'm having trouble processing your request right now. Would you like to speak with a human agent instead?";
  }
}
```

### 2. Crisp Integration

```javascript
// Crisp.chat integration for human handoff
window.$crisp = [];
window.CRISP_WEBSITE_ID = "your-crisp-website-id";

function initializeCrisp() {
  (function() {
    var d = document;
    var s = d.createElement("script");
    s.src = "https://client.crisp.chat/l.js";
    s.async = 1;
    d.getElementsByTagName("head")[0].appendChild(s);
  })();
}

function handoffToCrisp(userContext) {
  // Set user data
  $crisp.push(["set", "user:email", userContext.email]);
  $crisp.push(["set", "user:nickname", userContext.name]);
  $crisp.push(["set", "user:phone", userContext.phone]);
  
  // Set custom data
  $crisp.push(["set", "session:data", {
    project_type: userContext.projectType,
    budget_range: userContext.budgetRange,
    timeline: userContext.timeline,
    source: "chatbot_handoff"
  }]);
  
  // Open Crisp chat
  $crisp.push(["do", "chat:open"]);
  
  // Send context message
  $crisp.push(["do", "message:send", ["text", 
    `User was transferred from chatbot. Context: ${JSON.stringify(userContext)}`
  ]]);
}
```

### 3. Intercom Integration

```javascript
// Intercom integration
window.intercomSettings = {
  app_id: "your-intercom-app-id"
};

function initializeIntercom() {
  (function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/your-app-id';var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);};if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();
}

function handoffToIntercom(userContext) {
  Intercom('update', {
    email: userContext.email,
    name: userContext.name,
    phone: userContext.phone,
    custom_attributes: {
      project_type: userContext.projectType,
      budget_range: userContext.budgetRange,
      timeline: userContext.timeline,
      chatbot_context: JSON.stringify(userContext)
    }
  });
  
  Intercom('show');
}
```

## Training & Optimization Tips

### 1. Conversation Flow Optimization

```javascript
// A/B testing for conversation flows
const conversationVariants = {
  greeting_a: {
    message: "👋 Hi! I'm Alex from TechnoloWay. How can I help you today?",
    quickActions: ['Services', 'Quote', 'Portfolio', 'Contact']
  },
  greeting_b: {
    message: "Welcome to TechnoloWay! 🚀 Looking for software development services?",
    quickActions: ['Yes, tell me more', 'Get pricing', 'See our work', 'Talk to expert']
  }
};

function getGreetingVariant() {
  const variant = Math.random() < 0.5 ? 'greeting_a' : 'greeting_b';
  
  // Track variant for analytics
  analytics.track('chatbot_greeting_shown', {
    variant: variant,
    timestamp: new Date()
  });
  
  return conversationVariants[variant];
}
```

### 2. Intent Recognition Training

```javascript
// Enhanced intent detection with machine learning
const intentPatterns = {
  quote_request: [
    /\b(quote|price|pricing|cost|costs|budget|estimate|how much)\b/i,
    /\b(what.*cost|price.*for|how.*expensive)\b/i,
    /\b(ballpark|rough.*estimate|price.*range)\b/i
  ],
  
  services_inquiry: [
    /\b(service|services|what.*do|offerings|capabilities)\b/i,
    /\b(web.*development|mobile.*app|devops|consulting)\b/i,
    /\b(build.*website|create.*app|develop)\b/i
  ],
  
  portfolio_request: [
    /\b(portfolio|work|projects|examples|case.*studies)\b/i,
    /\b(show.*work|previous.*projects|what.*built)\b/i,
    /\b(demos|samples|references)\b/i
  ],
  
  human_handoff: [
    /\b(human|person|agent|representative|talk.*someone)\b/i,
    /\b(speak.*person|real.*person|live.*chat)\b/i,
    /\b(call.*me|phone.*call|schedule.*call)\b/i
  ]
};

function detectIntentML(message) {
  const scores = {};
  
  Object.keys(intentPatterns).forEach(intent => {
    scores[intent] = 0;
    
    intentPatterns[intent].forEach(pattern => {
      if (pattern.test(message)) {
        scores[intent] += 1;
      }
    });
  });
  
  // Find highest scoring intent
  const topIntent = Object.keys(scores).reduce((a, b) => 
    scores[a] > scores[b] ? a : b
  );
  
  return scores[topIntent] > 0 ? topIntent : 'general';
}
```

### 3. Analytics & Performance Tracking

```javascript
// Comprehensive analytics tracking
class ChatbotAnalytics {
  static trackEvent(event, data) {
    // Google Analytics
    if (typeof gtag !== 'undefined') {
      gtag('event', event, {
        event_category: 'chatbot',
        ...data
      });
    }
    
    // Custom analytics
    fetch('/api/analytics/chatbot', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        event,
        data,
        timestamp: new Date(),
        sessionId: this.getSessionId()
      })
    });
  }
  
  static trackConversationStart() {
    this.trackEvent('conversation_started', {
      source: document.referrer,
      page: window.location.pathname
    });
  }
  
  static trackIntentDetected(intent, confidence) {
    this.trackEvent('intent_detected', {
      intent,
      confidence
    });
  }
  
  static trackLeadQualified(leadData) {
    this.trackEvent('lead_qualified', {
      project_type: leadData.projectType,
      budget_range: leadData.budgetRange,
      timeline: leadData.timeline
    });
  }
  
  static trackHumanHandoff(reason) {
    this.trackEvent('human_handoff', {
      reason,
      message_count: this.getMessageCount()
    });
  }
}
```

### 4. Continuous Improvement

```javascript
// Feedback collection and improvement
function collectFeedback() {
  return {
    content: `How was your experience with our chatbot today?`,
    options: {
      quickActions: [
        { label: '😊 Great', value: 'feedback_great', icon: 'fas fa-smile' },
        { label: '😐 Okay', value: 'feedback_okay', icon: 'fas fa-meh' },
        { label: '😞 Poor', value: 'feedback_poor', icon: 'fas fa-frown' },
        { label: 'Skip', value: 'feedback_skip', icon: 'fas fa-forward' }
      ]
    }
  };
}

function handleFeedback(rating, details) {
  // Store feedback for analysis
  fetch('/api/chatbot/feedback', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      rating,
      details,
      conversation_id: getCurrentConversationId(),
      timestamp: new Date()
    })
  });
  
  // Thank user
  return {
    content: "Thank you for your feedback! It helps us improve our service. 🙏"
  };
}
```

## Deployment Checklist

### Pre-Launch
- [ ] Test all conversation flows
- [ ] Verify API integrations
- [ ] Test mobile responsiveness
- [ ] Set up analytics tracking
- [ ] Configure lead capture
- [ ] Test human handoff process
- [ ] Load test with concurrent users

### Post-Launch
- [ ] Monitor conversation success rates
- [ ] Track lead qualification metrics
- [ ] Analyze common user queries
- [ ] Optimize response times
- [ ] A/B test conversation flows
- [ ] Regular content updates
- [ ] Performance monitoring

### Security Considerations
- [ ] Sanitize user inputs
- [ ] Implement rate limiting
- [ ] Secure API endpoints
- [ ] Encrypt sensitive data
- [ ] GDPR compliance for EU users
- [ ] Regular security audits
