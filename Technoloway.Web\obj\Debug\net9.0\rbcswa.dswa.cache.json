{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["UZgWCM1NGQppv18B018jcf/YdnkknD2JedfC8SdIoLU=", "wdfKI4MyaQvKHaMwtQZP3EseeyJA8UFCVNX8S7WXEAg=", "wylW7vaTskS91Da1YAyFTLt935e3CjHNvrnSsBIxN9Y=", "vBSeJhYqtuXg9lZf4Hr2ZdpEsOdgMzmqOoMpUNarrHI=", "MoelAn4UoN18J6OC2eMEUldqfQLr/zBx8jwkxVyxfK4=", "XlsDJb2gmrKTJ/0zRPm+Pf0Q9uLSKyGU/StIkLjcyZk=", "AjCJ0xe231wal1p2BzIhnT/NvnrcHwByZh6RCRSN25c=", "8zFvuCCD0J/cRNCIxb1rjDn9YSqEQ2JG7WH0Vqplnik=", "KxL531La8+54SFlbyTdklUodp47r8uLxauBQXqJna6U=", "xNBPV0okdvrpWEehV3Q9UDRZnRB+HiWLlXb5Lqlu+1U=", "9iK4Y6nfSUhkuXSldoEuw/CZuR+3qCE5pJN3eG0HgLk=", "qElqMNaYa7Uk2n+hwExRhFhZkjLLo6/x3NebxbxavrQ=", "xKeSXz0h1JbXxxa/E1JmQ8hAY0wc38DowVHNUPWszLQ=", "HqpLIrSIOizRQEWRY6M3Z7wF088OICX858RI74dZKQk=", "Rsvh5iUtyf0ricoYFz7fzbnFsEaw45QGtVdR6GLNhW0=", "JooQ5I2lqMIKj++1ObmIzEWavhER+gd3YXPYL6hcDpY=", "wBM/rt4JkXUyh9W9HVSTPj8D04wJn0ejvQu6u/+w9KY=", "yr+xTBnjbJNGskWIXRKuKIocy6KGy8YPnzji6s4tCYI=", "fGGZdF4VHTDPqbses/LYvB65ofFSI8K0zQlwGj29ej4=", "xhSVpDGVZp4z9u6zF/KLoyJoMH1ZJCOoT3PJbumrYeI=", "S9R+j8QTaiM0ql1W5kRwVw/+L8dJQzzkDuCDErwKru8=", "KCtbonLTzQrgqOhgsKyNyGEToGDB29FCFR+tr+85Lkw=", "IdcXt4yqlq6/uVphEVwsBiShhdzMPbtRlipsZO9Pv+A=", "8D2fgsfR6xfhpge7ei45/s29EUcXzv23oXnxQ9xlgII=", "9cWsXQ37diXr7OBkw+vZ4s7IvtgDrkt6Mz+ysmbu8Hk=", "iyJug2qBK38Zh8jh/GpCB1/96vEZJjhOqI9zKu8CjLU=", "O+b1yKdO2dHChX3m81jAK73C16Mq1+x71v0AxTFsHWo=", "phWjkVil09VYJi2QOB8zRHS3Fsv67CIS6iTzqHvFI5w=", "VD5L4bafNMtiHjt/1i1o4y7vZuCB7NZSg4NKVaUl4LE=", "9m5/KzAZ++mcIHoKzx4X4yEkjI2gM0wFRoA7tHyJSb4=", "BUEqrnyafavln+zS4cgB/QTUIUObyOgsTrkrDH4lAkY=", "sGRibHbLjA05A2LwHB6fW3ejAKNKBHQSq8ZWGd4FX44=", "3SHh+8wRtVgfU4CrByvhzZAjazw/TCTTly2RHzOnmVw=", "hSvisAcsVfTLVmY4BUdVNps01lCyT1Qk913xE5PpL+Q=", "iUvX4ST6k8vKfJkTmQm96OVZQjy4fwqX4vSHdmOV4zg=", "HR/rg2AmLA9XZI2kPS6SaeIERbcCyu1kmUME7qu5yGU=", "CKDU+SaKNNQCFODlmoJ2XkGbHelb/zGufIwmByN1ySw=", "deHH37WNzTIj2gFK2/ZydVUhLNRvxk5t6hFQV8stkSg=", "m6e7PmGB1sX6PahlThYcYUd+ClT1VxNNXnCM2RZsg24=", "3ZsEeXGyjBs4k4sAV7+gMvUlrIq600GMZLOy14Jn1FQ=", "21WYpwp3YJvVVM4bd4eHKhbqVOtBe8dMrVXhz8Jrmvw=", "xjhxKWnXMpT61G1hdOH+LU6fMCLYFPbUtIqxwDP+y6k=", "fzqAe5y8CXaJj1NAK8RmazLNmpcUm/nw4rIESGwKd1A=", "QBlUMUsxQszDkM95C7mb+I/b+pCH9pUtfBt1RWFq9W0=", "WKkRA5AkExv1tGYKtuYCcPnthv93Cu+35TN8NUcUXWU=", "iZBmO/KSJEgV7hGrTM/r4Uwog2E+kd7DSzx+Kt/aQ4w=", "hU/BOX/+F73JXyXe7gSorYU1kqUY/SJ1qkQaRuyJ0gw=", "Z0OBMNzt5zks3ZIZjCBqp+3qlSS7xrO+SLBWR5y05Cc=", "GpYbmji7jD90Arzhm1KnOj/Z4CFDb8VqW5gBKHzTai0=", "ObRszTQoxolE89A8KKMJ0ZWQ66jBGt9KUGtjchIBcls=", "63htU0YLiVi7dG26TxiGvRFx5Jb486gGDZ9GjocJiOM=", "FE04UYo59OrRY0LTIDn5rs6/XtX8VAjBcmBExX3K8do=", "ZRoiwEP2jGHHhXt06l/vFyl6/ZIatHBDm6XMjr3XBVg=", "byPKKPTqZbLd/yUn1Z/92m3WwXDTJbzLq3cHKKHrBi8=", "JpjhtM7/0bHHht8GmGyiPt25bv/JoGcE0Uw7hDESYC0=", "WZqWSvOwwLepW0KWADDYvo5iVl64Rqe4oiy9keP0Qpk=", "hKIT9IA38fEJwPPSrc/iFcoAwtG37l0r+K37W63oFoU=", "HGFHF6F75gHPhvAiu5fmUUSaVhzvQFrZWPChYCF430w=", "lj+G4nd3EtlUtOdj2HvCrOKsmk3HTEh1zRTqEh86sgw=", "QDN09dZowm9I3M0AqmRU/bx7b2FLHnHfgXYS/D4AUfQ=", "NJX3YvcYFJ/EBbgo/7NTGxMTlsejWIRVT61FC6iM0xg=", "zp+F/ZYEIYtNEH32xMHxrDMwDeXB9QGDnL5IelJlOG0=", "x+4fk307jmW9fF/34Yc1ovRKEAXepW3Uzek0jmiFiTw=", "0PQpiozLHTr9Xztosk/r1O2XntxGn5zHjzGuN1WalzM=", "wUbioQE+KJsnWYpeEvdKIACP/yd/qJGH/WwgQSL2eqE=", "jf5pDxkXLXl2rVdVCHShgoUDW12Ia48OVskona6MIZU=", "ZUnb0B0dOXgu9ZQDpTtierXKkK/DW5Lawu8BUdLJEEY=", "ndjwoE095Yhm3aYAYhXUeTiIwIG/mcS4vMjrDo9resg=", "KwNgH6numyxapCB7HYNL+3HxUPkP8DPUyc1ppgVqREs=", "lVxDT/R/5P88P/IzlNy7PYKgmm/KLgpXNPWjeJ0HYMA=", "Y32jCcVX0NEHJfgvXkytss6tlcPCRDR8cakf2LjVMTY=", "wjuCiJF5wWUdZ43evIaunum0eL3uf9+YJTbSn6zKW9g=", "hDT1YtSMkkTiCO26bWeTZnhOQLV2DlXkInjRrqWkG2I=", "zxkL+3yHFHOWWZbUGgugthMldYngZpp7QDyxSMi39BI=", "f1NB95/eha0telb3cQu8V1GW0T7f/mQwRE1yb+HXiN0=", "f5ZLJKKYmu+1Kj7MdnrhYme+Wr936WWr7s/XHfP2kfM=", "4Nu0tL5XbSLzo6ThAAsc158vgeEZSYQeuv/+l03M+GY=", "5iqCzOTO4ZFyCg5BtWOrTjELukkEFF1vxLYO7Li890k=", "NUraU7stbXybEhN+KYg7SFd4M0ALOtNWdsV442FMGVc=", "s4djpZjkqbGel6b21O+bp1t7PzR5171yJt/LLj8QiGo=", "XIUsFXKQ/plS/3lUEmqDyRO8GhyeUAVWcn2JYQ69KXk=", "i55hJBs+beL3I9Dq4gMdOIUDC6pZLPLPqVRq3nJxR5U=", "WAAn//nyM7QYC6HxcnjAMJJaE22gmY9YaxHaSU35NZs=", "53Qgn28NbFrCRjNkkiFvqLf3pc9LMkHdoFksD19mMJ4=", "ZAcMq8PZxU1qv/4enwg0a7huKuuISSp8lxDhaCjRgBE=", "OOoqLCp1wHUET06Fdbwzt8S1KkdI5jZffBSkgu3c5hE=", "JCw85Kz2nhOAgS5HtbW06xUKNksJDyELCfOaOgOacnc=", "P4XWFqerVfofx28lZmcHOyB3K1Flm56eQhVH0RiHpjQ=", "TlHA5K+/Y1kyxfBgoTuEtb2dqE6rxQqGmPqmaaHoysU=", "YLAFHB8g1/pyckAXzGVAtL6os1Y6QWELs+MeZF42uJ4=", "zwgaKE7r3k/atLLH6BTvDBcWJU0boUAFhHtuD4PRaWg=", "z9hqcDEqVyoIbShbfBUwtHKJiQOVP+iBRxzz9Iimds0=", "0rISFfdI1e/78c/mg+Abc07gB/lKqkdOXjPR5sSqY6I=", "Y16ayDDBwnbh5QX4Y8hqrNIziHcmo0pvANL4DdLw8yU=", "W6jvDx0+Jujy6ZMNMzdPDBtKhoDT8WhHUogfFw/1Aic=", "ejPNdRZLQQHQtapZ8qFPFwYmppyz5D8z4ImroJtU3lE=", "nPDhCqhasaj9CYhfuI21ZU8oOW5ppr3uWhsHldGs83w=", "RBqVHdgNDUBsAeRG7fQvEEw4onSYwpuHuCNahbInvrM=", "3OctYn02T5jur+/E4Wuahzl6hZABDNTWv7SkyMphtfU=", "j8h7fVd1XvY1ZRo/+t0DVajsRwqbQUkDF4HxVgSgw9Y=", "tn8pX/quo0XWyvoXZQDR+oX2OA3ZuFRO11hgpYfvSzA=", "5qABebgOBo4wRRaHTaJVgU2LAuDLjvQ7/lQNkDbf9XA=", "G2sCWdE3qWQ978hrO7m6CSij+vXQzHIqzsNJB3C2eds=", "alI2o+yYc029V2anlx84AKMqgRHPA597lDdP1sSWrTU=", "TLBk2pZYWNN0blqWsAt1D/a9aw99TanvfL7gPqgaExE=", "BGAbCz2HQuu3jRmvjtzEyf06xDtLzn1+xJGVPtFlwKI=", "1AR8qqFaVenu8s0VzvwMobkt1XBm+TyTUVFsraR7euw=", "RfviXMlUMBUPUxvyo9icKJBoojhjlwaUB8Lz9L916SQ=", "pf85if+oIGyNq5nCrAdcR3a1lS4vLVMaoCExI6d/xjI=", "7eKJYafqAbajsmN0FILgjv5M5onvY75G/zjam3Mbgyc=", "QGv8IISedFIG8le3QdautD4pqIufQvGy8+uFD+OBDUM=", "TQnXH8qoGSlL724bd+WBLGbQkps2xLsgA3WTBOK0tv0=", "dTwcbMcLsDxN2Jk8s8pfALNZ00noFVho78x67n1F81o=", "PehuDqOp5R1MPSI2+AxYN7TLKboF3kUBbZW5o2ulews=", "YiSifUQofnkl6ppkmg5VNj+snC64jTQTPXXvh1YoEWI=", "CTe2ZhUCm+0PGM8zI0aq9C1sPgcCq34xwX2Px03z35E=", "kEBotLE+WeVVEc7KdY4Gt1R9tnDXi+DfVKEBnYgpj0c=", "u9ZgNLUYOB+6PGbkUqBMDhCLnbWx76HtlI5pgSBl+tY=", "pph/xTlmIZc1r1Lbd1ysRXzdaq5y19Y9vlhF59GEyC0=", "KHCRVz7gMUgTTaPb/MlT5V52UNbgeKLqY7kL1yQyCRU=", "RIaALHyMqdL8NtP9uBBja7R3vgq/JXx2pIg+MYfX0wI=", "e/QVH+WXGx9rGNqY24whwDmORfl9uKUgj9p5ACi5WpM=", "hGGT3s2joZ6w47xGBNSquPly0GoRlHVjLtLQIOfx3lA=", "bkkM7atDXo6S35rPsHRrRAiFi5EXx+xESfZf3o29MXQ=", "ilmsvQBdNG/PtAi3FVMoCnfK5yXmLGFo0V9xkSWrVlM=", "kwnSkQhR7+QSsHyRmTLbf5XKkDz9Zw6/DOYBQR8IoRE=", "vAzZepBAG+13ZHepvtSOS9Sb3of0eWs2aOJBnTtPbdg=", "tIeLv3IXmJAz5fTrrCe/+vLXvEXVghnr9AvUAm4JVYk=", "TmUOVBPfLT9LGo3sBnpfQ81UYbu0uPrsVHi1UBxMf0g=", "222WWpVYZO61IQhPTsrqvpOxBL4MkRU1XZWrIsWWIag=", "glMkIFB1WNnPeh6YgYzSf0sdiUtK3EDODblEdRVirYE=", "o0GWB8ODDolwr/3uLW1jSEiG6ur7XHxINXzIDfOVoGA=", "KTBBD0bO55llf3Xo3yVioHNs43J7QSpKx0i9W+7iv1M=", "OkqAFv7eeVPuzZG8fjb2GdGRhPXiQEk+I98SpaUi56g=", "swhBwlshBozNuw/D8U+9MCYabAwZwXFZ+hMzH3MVxwA=", "M0tuP2arQE1EG1ajrjPNpP6z6kaVRqlaBA1mHPTeksM=", "B8Cd3b4LS+wPNevFHCAxCmO1QBxWD2Z06qy2ndJAe70=", "QweWZzE8onXfjf1WSWqqHYRhveotSQCjZwWYtTleZ7o=", "hW6garCyX9jGcSZREU54O+fepR2JJBPztlBR0hugFVA=", "TIehSJx7Pp/Lb9jVyB92t3GHVCXfuZcf4qkQDr7P928=", "BKKGOSu0g7Q4V9tJbHABEPBYRla1X36bHId2FLxRoKs=", "bg1z7RuAW3ok9r3nnpmZuZqtcYIuilyP2qAMzJM9iHI=", "geIGhd/D4quqsfFQyJq9BgaQEoLjhnd2H1F/T/NSxXA="], "CachedAssets": {"UZgWCM1NGQppv18B018jcf/YdnkknD2JedfC8SdIoLU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ofa40bqe71-b9sayid5wm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "css/site.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-06-01T14:56:49.2456914+00:00"}, "wdfKI4MyaQvKHaMwtQZP3EseeyJA8UFCVNX8S7WXEAg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j9swiz3yzq-90yqlj465b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "favicon.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "fxo9vb9gbf", "Integrity": "cwHpDnBTabxDzOHXFPOawb7ZlL3eysr4s1HfAD/K3vA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\favicon.ico", "FileLength": 9541, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "wylW7vaTskS91Da1YAyFTLt935e3CjHNvrnSsBIxN9Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vltxs1u3vm-xtxxf3hu2r.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "js/site.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-06-01T14:56:49.2605821+00:00"}, "vBSeJhYqtuXg9lZf4Hr2ZdpEsOdgMzmqOoMpUNarrHI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\14ipv7q33s-bqjiyaj88i.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "MoelAn4UoN18J6OC2eMEUldqfQLr/zBx8jwkxVyxfK4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rh65p086id-c2jlpeoesf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-01T14:56:49.3240739+00:00"}, "XlsDJb2gmrKTJ/0zRPm+Pf0Q9uLSKyGU/StIkLjcyZk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\eb27mqwgz2-erw9l3u2r3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-01T14:56:49.974646+00:00"}, "AjCJ0xe231wal1p2BzIhnT/NvnrcHwByZh6RCRSN25c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9glsckmvxo-aexeepp0ev.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-01T14:56:49.3401603+00:00"}, "8zFvuCCD0J/cRNCIxb1rjDn9YSqEQ2JG7WH0Vqplnik=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hh8ihyobdx-d7shbmvgxk.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-01T14:56:49.9271346+00:00"}, "KxL531La8+54SFlbyTdklUodp47r8uLxauBQXqJna6U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bww8ud00yd-ausgxo2sd3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-01T14:56:49.3401603+00:00"}, "xNBPV0okdvrpWEehV3Q9UDRZnRB+HiWLlXb5Lqlu+1U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\edcrak57d7-k8d9w2qqmf.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-01T14:56:49.9589975+00:00"}, "9iK4Y6nfSUhkuXSldoEuw/CZuR+3qCE5pJN3eG0HgLk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\sf8kf2lula-cosvhxvwiu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-01T14:56:49.3240739+00:00"}, "qElqMNaYa7Uk2n+hwExRhFhZkjLLo6/x3NebxbxavrQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\3tzsqhslk9-ub07r2b239.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-01T14:56:50.0062272+00:00"}, "xKeSXz0h1JbXxxa/E1JmQ8hAY0wc38DowVHNUPWszLQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\oxk5o6czdw-fvhpjtyr6v.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-01T14:56:49.3240739+00:00"}, "HqpLIrSIOizRQEWRY6M3Z7wF088OICX858RI74dZKQk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\k8b25g0zwc-b7pk76d08c.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-01T14:56:50.0062272+00:00"}, "Rsvh5iUtyf0ricoYFz7fzbnFsEaw45QGtVdR6GLNhW0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8axix7wldr-fsbi9cje9m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-01T14:56:50.1485699+00:00"}, "JooQ5I2lqMIKj++1ObmIzEWavhER+gd3YXPYL6hcDpY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\d5z9sfpxbi-rzd6atqjts.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-01T14:56:50.1958525+00:00"}, "wBM/rt4JkXUyh9W9HVSTPj8D04wJn0ejvQu6u/+w9KY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8428c4e9i-ee0r1s7dh0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-01T14:56:49.3401603+00:00"}, "yr+xTBnjbJNGskWIXRKuKIocy6KGy8YPnzji6s4tCYI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tg53r17ghy-dxx9fxp4il.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-01T14:56:49.2615862+00:00"}, "fGGZdF4VHTDPqbses/LYvB65ofFSI8K0zQlwGj29ej4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nlrl0f3xs3-jd9uben2k1.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-01T14:56:49.2749399+00:00"}, "xhSVpDGVZp4z9u6zF/KLoyJoMH1ZJCOoT3PJbumrYeI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1fc4q00scq-khv3u5hwcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-01T14:56:49.3560468+00:00"}, "S9R+j8QTaiM0ql1W5kRwVw/+L8dJQzzkDuCDErwKru8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xykfy6qmd-r4e9w2rdcm.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "KCtbonLTzQrgqOhgsKyNyGEToGDB29FCFR+tr+85Lkw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vsrbd71spn-lcd1t2u6c8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-01T14:56:49.6985067+00:00"}, "IdcXt4yqlq6/uVphEVwsBiShhdzMPbtRlipsZO9Pv+A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cynp17w084-c2oey78nd0.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-01T14:56:49.3401603+00:00"}, "8D2fgsfR6xfhpge7ei45/s29EUcXzv23oXnxQ9xlgII=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7o8oyvng68-tdbxkamptv.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-01T14:56:49.939387+00:00"}, "9cWsXQ37diXr7OBkw+vZ4s7IvtgDrkt6Mz+ysmbu8Hk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h9vwtoirpy-j5mq2jizvt.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-01T14:56:49.9589975+00:00"}, "iyJug2qBK38Zh8jh/GpCB1/96vEZJjhOqI9zKu8CjLU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\o5k23vqhrk-06098lyss8.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-01T14:56:50.0219413+00:00"}, "O+b1yKdO2dHChX3m81jAK73C16Mq1+x71v0AxTFsHWo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\iznc766avz-nvvlpmu67g.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-01T14:56:49.9428205+00:00"}, "phWjkVil09VYJi2QOB8zRHS3Fsv67CIS6iTzqHvFI5w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ns4583i71s-s35ty4nyc5.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-01T14:56:50.1958525+00:00"}, "VD5L4bafNMtiHjt/1i1o4y7vZuCB7NZSg4NKVaUl4LE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\47012z8l2l-pj5nd1wqec.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-01T14:56:51.5450299+00:00"}, "9m5/KzAZ++mcIHoKzx4X4yEkjI2gM0wFRoA7tHyJSb4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4vzefxwp2m-46ein0sx1k.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-01T14:56:51.6298279+00:00"}, "BUEqrnyafavln+zS4cgB/QTUIUObyOgsTrkrDH4lAkY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bh4v3mdph9-v0zj4ognzu.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-01T14:56:49.8410738+00:00"}, "sGRibHbLjA05A2LwHB6fW3ejAKNKBHQSq8ZWGd4FX44=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l186vlgaag-37tfw0ft22.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-01T14:56:50.1958525+00:00"}, "3SHh+8wRtVgfU4CrByvhzZAjazw/TCTTly2RHzOnmVw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\rsc7qacj1u-hrwsygsryq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-01T14:56:51.2360531+00:00"}, "hSvisAcsVfTLVmY4BUdVNps01lCyT1Qk913xE5PpL+Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z408qb6q7a-pk9g2wxc8p.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-01T14:56:51.5577908+00:00"}, "iUvX4ST6k8vKfJkTmQm96OVZQjy4fwqX4vSHdmOV4zg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qfr28sqcy1-ft3s53vfgj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-01T14:56:49.3401603+00:00"}, "HR/rg2AmLA9XZI2kPS6SaeIERbcCyu1kmUME7qu5yGU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0k1i85ntyh-6cfz1n2cew.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-01T14:56:49.3240739+00:00"}, "CKDU+SaKNNQCFODlmoJ2XkGbHelb/zGufIwmByN1ySw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xa379ftczi-6pdc2jztkx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-01T14:56:49.3499624+00:00"}, "deHH37WNzTIj2gFK2/ZydVUhLNRvxk5t6hFQV8stkSg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lr5hmrr0j7-493y06b0oq.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-01T14:56:49.7050868+00:00"}, "m6e7PmGB1sX6PahlThYcYUd+ClT1VxNNXnCM2RZsg24=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ivy2s9gwh5-iovd86k7lj.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-01T14:56:50.259405+00:00"}, "3ZsEeXGyjBs4k4sAV7+gMvUlrIq600GMZLOy14Jn1FQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\higufxz8op-vr1egmr9el.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-01T14:56:50.560003+00:00"}, "21WYpwp3YJvVVM4bd4eHKhbqVOtBe8dMrVXhz8Jrmvw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\s3tglgz8hr-kbrnm935zg.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-01T14:56:51.400103+00:00"}, "xjhxKWnXMpT61G1hdOH+LU6fMCLYFPbUtIqxwDP+y6k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vw9sls9bhj-jj8uyg4cgr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-01T14:56:49.8410738+00:00"}, "fzqAe5y8CXaJj1NAK8RmazLNmpcUm/nw4rIESGwKd1A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ym8tkpcl2i-y7v9cxd14o.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-01T14:56:50.0062272+00:00"}, "QBlUMUsxQszDkM95C7mb+I/b+pCH9pUtfBt1RWFq9W0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zzna4nrm5w-notf2xhcfb.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-01T14:56:50.2117449+00:00"}, "WKkRA5AkExv1tGYKtuYCcPnthv93Cu+35TN8NUcUXWU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\igscs4x0yk-h1s4sie4z3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-01T14:56:50.5516178+00:00"}, "iZBmO/KSJEgV7hGrTM/r4Uwog2E+kd7DSzx+Kt/aQ4w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zap00c0tb5-63fj8s7r0e.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-01T14:56:49.9271346+00:00"}, "hU/BOX/+F73JXyXe7gSorYU1kqUY/SJ1qkQaRuyJ0gw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pogzkicjpz-0j3bgjxly4.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-01T14:56:50.1642241+00:00"}, "Z0OBMNzt5zks3ZIZjCBqp+3qlSS7xrO+SLBWR5y05Cc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\j3hwsq15kh-47otxtyo56.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-01T14:56:50.2276022+00:00"}, "GpYbmji7jD90Arzhm1KnOj/Z4CFDb8VqW5gBKHzTai0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q3naettu8c-4v8eqarkd7.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-01T14:56:50.418351+00:00"}, "ObRszTQoxolE89A8KKMJ0ZWQ66jBGt9KUGtjchIBcls=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ldxy2n3w6q-356vix0kms.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-01T14:56:49.939387+00:00"}, "63htU0YLiVi7dG26TxiGvRFx5Jb486gGDZ9GjocJiOM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypthv7q62w-83jwlth58m.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-01T14:56:50.0376601+00:00"}, "FE04UYo59OrRY0LTIDn5rs6/XtX8VAjBcmBExX3K8do=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t6e3b82hrf-mrlpezrjn3.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-01T14:56:49.2585782+00:00"}, "ZRoiwEP2jGHHhXt06l/vFyl6/ZIatHBDm6XMjr3XBVg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uocis9wxbx-lzl9nlhx6b.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-01T14:56:49.2713948+00:00"}, "byPKKPTqZbLd/yUn1Z/92m3WwXDTJbzLq3cHKKHrBi8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2tikzqlmtu-ag7o75518u.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-01T14:56:49.3123094+00:00"}, "JpjhtM7/0bHHht8GmGyiPt25bv/JoGcE0Uw7hDESYC0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9muusbdg0a-x0q3zqp4vz.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery-validation/LICENSE.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-01T14:56:49.3223298+00:00"}, "WZqWSvOwwLepW0KWADDYvo5iVl64Rqe4oiy9keP0Qpk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tpsq5dibur-0i3buxo5is.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-01T14:56:50.2117449+00:00"}, "hKIT9IA38fEJwPPSrc/iFcoAwtG37l0r+K37W63oFoU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t0qo7o574p-o1o13a6vjx.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-01T14:56:49.3240739+00:00"}, "HGFHF6F75gHPhvAiu5fmUUSaVhzvQFrZWPChYCF430w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b9lhb08218-ttgo8qnofa.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-01T14:56:50.0219413+00:00"}, "lj+G4nd3EtlUtOdj2HvCrOKsmk3HTEh1zRTqEh86sgw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\a3ygkwa5zy-2z0ns9nrw6.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-01T14:56:49.9271346+00:00"}, "QDN09dZowm9I3M0AqmRU/bx7b2FLHnHfgXYS/D4AUfQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nezrqnk58p-muycvpuwrr.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-01T14:56:50.0062272+00:00"}, "NJX3YvcYFJ/EBbgo/7NTGxMTlsejWIRVT61FC6iM0xg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v0zgfp0y55-87fc7y1x7t.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/dist/jquery.slim.min.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "zp+F/ZYEIYtNEH32xMHxrDMwDeXB9QGDnL5IelJlOG0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\yfc7ypekbg-mlv21k5csn.gz", "SourceId": "Microsoft.AspNetCore.Identity.UI", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "Identity", "RelativePath": "lib/jquery/LICENSE.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.identity.ui\\9.0.5\\staticwebassets\\V5\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-01T14:56:49.939387+00:00"}, "x+4fk307jmW9fF/34Yc1ovRKEAXepW3Uzek0jmiFiTw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ngr7uwcar-muz6j4ombm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/admin#[.{fingerprint=muz6j4ombm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8w614is3cs", "Integrity": "2Jr8LxCUoqyw2eLNcSoz2h6UB8Dpr8YzQ2WSAAhfoSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\admin.css", "FileLength": 6904, "LastWriteTime": "2025-06-01T14:56:49.8164517+00:00"}, "0PQpiozLHTr9Xztosk/r1O2XntxGn5zHjzGuN1WalzM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vzqep69h03-ttlnujb3md.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/chatbot#[.{fingerprint=ttlnujb3md}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "i3oc660vng", "Integrity": "Ap0MJU654qvh5mJKl0/CazXUcGLSMDuvMETk+sIdhVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\chatbot.css", "FileLength": 2529, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "wUbioQE+KJsnWYpeEvdKIACP/yd/qJGH/WwgQSL2eqE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pn3zjfqylx-g07bnx142d.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/client#[.{fingerprint=g07bnx142d}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u372l94rk3", "Integrity": "hnZ3ayaGZFLAG6FjKMU6gjkmJLNka/tR9IAAQRpSPoc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\client.css", "FileLength": 4346, "LastWriteTime": "2025-06-01T14:56:49.8164517+00:00"}, "jf5pDxkXLXl2rVdVCHShgoUDW12Ia48OVskona6MIZU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\elvx5emtjm-wilnnc3w1m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/custom#[.{fingerprint=wilnnc3w1m}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k58f1us78f", "Integrity": "CDABZ8Got8gWH2Wic8T6MC1izVu156P7c25rWmLiqLI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\custom.css", "FileLength": 212, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "ZUnb0B0dOXgu9ZQDpTtierXKkK/DW5Lawu8BUdLJEEY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ypbfssc6px-1edbnb0gu6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/hero-slideshow#[.{fingerprint=1edbnb0gu6}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kg1721j3l3", "Integrity": "3CYDHawx/sbpzjIQoN7+wnjWSSXtjNUj7txCQ3kydJI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\hero-slideshow.css", "FileLength": 1718, "LastWriteTime": "2025-06-01T14:56:49.8164517+00:00"}, "ndjwoE095Yhm3aYAYhXUeTiIwIG/mcS4vMjrDo9resg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\xtt04yhxc6-0q4my8jvfl.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/modern-homepage#[.{fingerprint=0q4my8jvfl}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rcvxhwxgzm", "Integrity": "BaKfeGlWkAeD/OI5bepfDPACZjrHdgAWyxNU1bDp3LE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\modern-homepage.css", "FileLength": 14185, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "KwNgH6numyxapCB7HYNL+3HxUPkP8DPUyc1ppgVqREs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7ejlrje5jg-8znpj9knio.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "css/site#[.{fingerprint=8znpj9knio}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tb45argtbf", "Integrity": "KXsoPJcXUP9jdH3J4w7jvn/bvOC4Ia8KvN2KNFAyres=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\css\\site.css", "FileLength": 2281, "LastWriteTime": "2025-06-01T14:56:49.2456914+00:00"}, "lVxDT/R/5P88P/IzlNy7PYKgmm/KLgpXNPWjeJ0HYMA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\03l1rte6gq-61n19gt1b8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-06-01T14:56:49.2693896+00:00"}, "Y32jCcVX0NEHJfgvXkytss6tlcPCRDR8cakf2LjVMTY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ktm00ucnju-wgrwvlfr5s.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/delphi#[.{fingerprint=wgrwvlfr5s}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tkbigiuy64", "Integrity": "HQM1ysqWHmUnuAlp3aK0g+H3KTdRrGOY3H0Vq22/RWA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\delphi.svg", "FileLength": 1935, "LastWriteTime": "2025-06-01T14:56:49.2535401+00:00"}, "wjuCiJF5wWUdZ43evIaunum0eL3uf9+YJTbSn6zKW9g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\qveyjng0ys-nedvdbx254.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/haskell#[.{fingerprint=nedvdbx254}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "edsoh1itdo", "Integrity": "olpJlaPKyLgPjxsEUdPEwIwMpvYmE5KFaI68j+OJmJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\haskell.svg", "FileLength": 336, "LastWriteTime": "2025-06-01T14:56:49.2585782+00:00"}, "hDT1YtSMkkTiCO26bWeTZnhOQLV2DlXkInjRrqWkG2I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ikbcgss9f7-k8cq9l2kib.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/nodejs#[.{fingerprint=k8cq9l2kib}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lpoc1bcrbp", "Integrity": "GTXsRzccKflDEkzr6cT5Iq+5KyyN46VgzOgbjaIisWo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\nodejs.svg", "FileLength": 721, "LastWriteTime": "2025-06-01T14:56:49.2673865+00:00"}, "zxkL+3yHFHOWWZbUGgugthMldYngZpp7QDyxSMi39BI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\nnketcjwya-1x8wi64s97.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/python#[.{fingerprint=1x8wi64s97}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j6yyh4b7nm", "Integrity": "i5K/+SeOT5dWe0FWBa0GIm1JLWXKxGhZVNxsYkLDrns=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\python.svg", "FileLength": 734, "LastWriteTime": "2025-06-01T14:56:49.2585782+00:00"}, "f1NB95/eha0telb3cQu8V1GW0T7f/mQwRE1yb+HXiN0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\uham8ign5j-emsbkume29.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technologies/swift#[.{fingerprint=emsbkume29}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rvaavaxrke", "Integrity": "afO1CiL+LYXrmSV0hG/nzRY58iy/trDc+maeMmc1q8Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technologies\\swift.svg", "FileLength": 619, "LastWriteTime": "2025-06-01T14:56:49.2693896+00:00"}, "f5ZLJKKYmu+1Kj7MdnrhYme+Wr936WWr7s/XHfP2kfM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\10owx4zk9i-y36mr8d3re.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "images/technoloway-logo#[.{fingerprint=y36mr8d3re}]?.svg.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "78ry7ld12l", "Integrity": "qVZsciNX4bvJyG/NFAFG6/1bWRdPLgFZLpfMSEqLyZo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\images\\technoloway-logo.svg", "FileLength": 110221, "LastWriteTime": "2025-06-01T14:56:49.2774481+00:00"}, "4Nu0tL5XbSLzo6ThAAsc158vgeEZSYQeuv/+l03M+GY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\02bln8vcid-6de5fjlu8q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/admin#[.{fingerprint=6de5fjlu8q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bkj3u4c828", "Integrity": "y7OG3+AqATtB8KCnImHlz9DaMCW/SvkgUDW/ww3awkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\admin.js", "FileLength": 5688, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "5iqCzOTO4ZFyCg5BtWOrTjELukkEFF1vxLYO7Li890k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wb4hq6364b-q3rvw56cw8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/chatbot#[.{fingerprint=q3rvw56cw8}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lamsm00ivo", "Integrity": "I0ro0wUEaHukhFjPosOr+Bfcgge4Qpt9xMmjw6prEcY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\chatbot.js", "FileLength": 6566, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "NUraU7stbXybEhN+KYg7SFd4M0ALOtNWdsV442FMGVc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\v8u4sxh3fr-3xu3gfddrx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/client#[.{fingerprint=3xu3gfddrx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kkcwqz3pwi", "Integrity": "pD2v2NXmu8dIQdlAvDxSQC28LtmTLNKzC22UlumYprU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\client.js", "FileLength": 390, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "s4djpZjkqbGel6b21O+bp1t7PzR5171yJt/LLj8QiGo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\h4d7zuu3lf-rd81zyuzir.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/hero-slideshow#[.{fingerprint=rd81zyuzir}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "6lq72lcfcg", "Integrity": "pkfBPs6ownzjPebnXD0fcxTJYLLrd3SJgTGnCXXCxXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\hero-slideshow.js", "FileLength": 1678, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "XIUsFXKQ/plS/3lUEmqDyRO8GhyeUAVWcn2JYQ69KXk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\br1wthuj05-wi7ss39h6q.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/homepage-animations#[.{fingerprint=wi7ss39h6q}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iftq384gbj", "Integrity": "gqWjPa9AQ/+yPWfZCbXLhtYUgjFs8Oxn+qn1YXdxUNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\homepage-animations.js", "FileLength": 2761, "LastWriteTime": "2025-06-01T14:56:49.3183219+00:00"}, "i55hJBs+beL3I9Dq4gMdOIUDC6pZLPLPqVRq3nJxR5U=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\5eaaj8jli5-2svxw61t86.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "js/site#[.{fingerprint=2svxw61t86}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o75gd3adie", "Integrity": "ZHcCB5qku88L0iD5sq5euDqrhPm9N+4MeMk/tRrrgIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\js\\site.js", "FileLength": 769, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "WAAn//nyM7QYC6HxcnjAMJJaE22gmY9YaxHaSU35NZs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\huncoxf4nc-bqjiyaj88i.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-06-01T14:56:49.3163183+00:00"}, "53Qgn28NbFrCRjNkkiFvqLf3pc9LMkHdoFksD19mMJ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2jnr46nmpc-c2jlpeoesf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-06-01T14:56:49.3223298+00:00"}, "ZAcMq8PZxU1qv/4enwg0a7huKuuISSp8lxDhaCjRgBE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wmzyo7xuhb-erw9l3u2r3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-06-01T14:56:49.6985067+00:00"}, "OOoqLCp1wHUET06Fdbwzt8S1KkdI5jZffBSkgu3c5hE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\djyxqsu5pv-aexeepp0ev.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-06-01T14:56:49.2535401+00:00"}, "JCw85Kz2nhOAgS5HtbW06xUKNksJDyELCfOaOgOacnc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2sbuxwf498-d7shbmvgxk.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-06-01T14:56:49.2585782+00:00"}, "P4XWFqerVfofx28lZmcHOyB3K1Flm56eQhVH0RiHpjQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fe676pkbci-ausgxo2sd3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-06-01T14:56:49.2555457+00:00"}, "TlHA5K+/Y1kyxfBgoTuEtb2dqE6rxQqGmPqmaaHoysU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2n44t9y1oe-k8d9w2qqmf.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-06-01T14:56:49.2585782+00:00"}, "YLAFHB8g1/pyckAXzGVAtL6os1Y6QWELs+MeZF42uJ4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2yiqndbjob-cosvhxvwiu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-06-01T14:56:49.2713948+00:00"}, "zwgaKE7r3k/atLLH6BTvDBcWJU0boUAFhHtuD4PRaWg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6laymexpp1-ub07r2b239.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "z9hqcDEqVyoIbShbfBUwtHKJiQOVP+iBRxzz9Iimds0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e23n28kbl6-fvhpjtyr6v.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "0rISFfdI1e/78c/mg+Abc07gB/lKqkdOXjPR5sSqY6I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\0xdmrg7j3x-b7pk76d08c.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-06-01T14:56:49.2693896+00:00"}, "Y16ayDDBwnbh5QX4Y8hqrNIziHcmo0pvANL4DdLw8yU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7msow90u6m-fsbi9cje9m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-06-01T14:56:49.3123094+00:00"}, "W6jvDx0+Jujy6ZMNMzdPDBtKhoDT8WhHUogfFw/1Aic=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\y6lzeo23io-rzd6atqjts.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "ejPNdRZLQQHQtapZ8qFPFwYmppyz5D8z4ImroJtU3lE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\b1eq4g63qz-ee0r1s7dh0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "nPDhCqhasaj9CYhfuI21ZU8oOW5ppr3uWhsHldGs83w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\q7xywl88yg-dxx9fxp4il.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-06-01T14:56:49.3123094+00:00"}, "RBqVHdgNDUBsAeRG7fQvEEw4onSYwpuHuCNahbInvrM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tl4yfr4qca-jd9uben2k1.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-06-01T14:56:49.3143144+00:00"}, "3OctYn02T5jur+/E4Wuahzl6hZABDNTWv7SkyMphtfU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8xb1bhl2rn-khv3u5hwcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-06-01T14:56:49.3123094+00:00"}, "j8h7fVd1XvY1ZRo/+t0DVajsRwqbQUkDF4HxVgSgw9Y=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\1ju4p3k34w-r4e9w2rdcm.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-06-01T14:56:49.3163183+00:00"}, "tn8pX/quo0XWyvoXZQDR+oX2OA3ZuFRO11hgpYfvSzA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\jyl4075mxi-lcd1t2u6c8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-06-01T14:56:49.3183219+00:00"}, "5qABebgOBo4wRRaHTaJVgU2LAuDLjvQ7/lQNkDbf9XA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zul1z3i66m-c2oey78nd0.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-06-01T14:56:49.7050868+00:00"}, "G2sCWdE3qWQ978hrO7m6CSij+vXQzHIqzsNJB3C2eds=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tj8j7uiu1e-tdbxkamptv.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-06-01T14:56:49.2535401+00:00"}, "alI2o+yYc029V2anlx84AKMqgRHPA597lDdP1sSWrTU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\l6ou9js1o2-j5mq2jizvt.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-06-01T14:56:49.26359+00:00"}, "TLBk2pZYWNN0blqWsAt1D/a9aw99TanvfL7gPqgaExE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\z3s3pqtpk8-06098lyss8.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-06-01T14:56:49.2535401+00:00"}, "BGAbCz2HQuu3jRmvjtzEyf06xDtLzn1+xJGVPtFlwKI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\pkmk90gfad-nvvlpmu67g.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-06-01T14:56:49.2615862+00:00"}, "1AR8qqFaVenu8s0VzvwMobkt1XBm+TyTUVFsraR7euw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zktux6y5y4-s35ty4nyc5.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-06-01T14:56:49.2749399+00:00"}, "RfviXMlUMBUPUxvyo9icKJBoojhjlwaUB8Lz9L916SQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\26o4et715b-pj5nd1wqec.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-06-01T14:56:49.2713948+00:00"}, "pf85if+oIGyNq5nCrAdcR3a1lS4vLVMaoCExI6d/xjI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\7gp52xdync-46ein0sx1k.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-06-01T14:56:49.3163183+00:00"}, "7eKJYafqAbajsmN0FILgjv5M5onvY75G/zjam3Mbgyc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e7b6u3x55w-v0zj4ognzu.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-06-01T14:56:49.2794515+00:00"}, "QGv8IISedFIG8le3QdautD4pqIufQvGy8+uFD+OBDUM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9zg9c6chb7-37tfw0ft22.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-06-01T14:56:49.3183219+00:00"}, "TQnXH8qoGSlL724bd+WBLGbQkps2xLsgA3WTBOK0tv0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\33czbugvzg-hrwsygsryq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-06-01T14:56:49.3183219+00:00"}, "dTwcbMcLsDxN2Jk8s8pfALNZ00noFVho78x67n1F81o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2d5epgijjo-pk9g2wxc8p.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "PehuDqOp5R1MPSI2+AxYN7TLKboF3kUBbZW5o2ulews=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\t63suxyd54-ft3s53vfgj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-06-01T14:56:49.828015+00:00"}, "YiSifUQofnkl6ppkmg5VNj+snC64jTQTPXXvh1YoEWI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5r96tufml-6cfz1n2cew.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-06-01T14:56:49.832118+00:00"}, "CTe2ZhUCm+0PGM8zI0aq9C1sPgcCq34xwX2Px03z35E=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\wegvxza2ge-6pdc2jztkx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-06-01T14:56:49.3223298+00:00"}, "kEBotLE+WeVVEc7KdY4Gt1R9tnDXi+DfVKEBnYgpj0c=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g09mkto0sq-493y06b0oq.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-06-01T14:56:49.8004766+00:00"}, "u9ZgNLUYOB+6PGbkUqBMDhCLnbWx76HtlI5pgSBl+tY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\4mg52w1lo3-iovd86k7lj.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "pph/xTlmIZc1r1Lbd1ysRXzdaq5y19Y9vlhF59GEyC0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\lh2isbcsad-vr1egmr9el.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-06-01T14:56:49.8260126+00:00"}, "KHCRVz7gMUgTTaPb/MlT5V52UNbgeKLqY7kL1yQyCRU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g1ftby5py3-kbrnm935zg.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-06-01T14:56:49.2615862+00:00"}, "RIaALHyMqdL8NtP9uBBja7R3vgq/JXx2pIg+MYfX0wI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\vphg83v0d2-jj8uyg4cgr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-06-01T14:56:49.2605821+00:00"}, "e/QVH+WXGx9rGNqY24whwDmORfl9uKUgj9p5ACi5WpM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\hd7xpsd4po-y7v9cxd14o.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-06-01T14:56:49.26359+00:00"}, "hGGT3s2joZ6w47xGBNSquPly0GoRlHVjLtLQIOfx3lA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2626qsfhg1-notf2xhcfb.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "bkkM7atDXo6S35rPsHRrRAiFi5EXx+xESfZf3o29MXQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\u8c68735sf-h1s4sie4z3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "ilmsvQBdNG/PtAi3FVMoCnfK5yXmLGFo0V9xkSWrVlM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\fij7bwzp8x-63fj8s7r0e.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-06-01T14:56:49.2749399+00:00"}, "kwnSkQhR7+QSsHyRmTLbf5XKkDz9Zw6/DOYBQR8IoRE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\cji4aywtqv-0j3bgjxly4.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}, "vAzZepBAG+13ZHepvtSOS9Sb3of0eWs2aOJBnTtPbdg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\9mvculopuj-47otxtyo56.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "tIeLv3IXmJAz5fTrrCe/+vLXvEXVghnr9AvUAm4JVYk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\bf5okwooxo-4v8eqarkd7.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "TmUOVBPfLT9LGo3sBnpfQ81UYbu0uPrsVHi1UBxMf0g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\g10371glbs-356vix0kms.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-06-01T14:56:49.3163183+00:00"}, "222WWpVYZO61IQhPTsrqvpOxBL4MkRU1XZWrIsWWIag=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2iu5gkm6iv-83jwlth58m.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-06-01T14:56:49.3183219+00:00"}, "glMkIFB1WNnPeh6YgYzSf0sdiUtK3EDODblEdRVirYE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ahn74umtfx-mrlpezrjn3.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-06-01T14:56:49.8004766+00:00"}, "o0GWB8ODDolwr/3uLW1jSEiG6ur7XHxINXzIDfOVoGA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8k9meis7w4-lzl9nlhx6b.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-06-01T14:56:49.8184545+00:00"}, "KTBBD0bO55llf3Xo3yVioHNs43J7QSpKx0i9W+7iv1M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\6jty019ari-ag7o75518u.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-06-01T14:56:49.3103056+00:00"}, "OkqAFv7eeVPuzZG8fjb2GdGRhPXiQEk+I98SpaUi56g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\w5qk1pgd3r-x0q3zqp4vz.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-06-01T14:56:49.3123094+00:00"}, "swhBwlshBozNuw/D8U+9MCYabAwZwXFZ+hMzH3MVxwA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\64rjhqzavh-0i3buxo5is.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-06-01T14:56:49.3203259+00:00"}, "M0tuP2arQE1EG1ajrjPNpP6z6kaVRqlaBA1mHPTeksM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\dmzx9f6yhi-o1o13a6vjx.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-06-01T14:56:49.7050868+00:00"}, "B8Cd3b4LS+wPNevFHCAxCmO1QBxWD2Z06qy2ndJAe70=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\zydbwp8g7y-ttgo8qnofa.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-06-01T14:56:49.2555457+00:00"}, "QweWZzE8onXfjf1WSWqqHYRhveotSQCjZwWYtTleZ7o=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\tivk7iw1cd-2z0ns9nrw6.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-06-01T14:56:49.26359+00:00"}, "hW6garCyX9jGcSZREU54O+fepR2JJBPztlBR0hugFVA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\2v4zxome5j-muycvpuwrr.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-06-01T14:56:49.2605821+00:00"}, "TIehSJx7Pp/Lb9jVyB92t3GHVCXfuZcf4qkQDr7P928=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\e15jphxp1u-87fc7y1x7t.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-06-01T14:56:49.2713948+00:00"}, "BKKGOSu0g7Q4V9tJbHABEPBYRla1X36bHId2FLxRoKs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\8ezk5r3swy-mlv21k5csn.gz", "SourceId": "Technoloway.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "bg1z7RuAW3ok9r3nnpmZuZqtcYIuilyP2qAMzJM9iHI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\ot0u7hstz0-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\bundle\\Technoloway.Web.styles.css", "FileLength": 545, "LastWriteTime": "2025-06-01T14:56:49.2926235+00:00"}, "geIGhd/D4quqsfFQyJq9BgaQEoLjhnd2H1F/T/NSxXA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\on9a315cdm-btisj24con.gz", "SourceId": "Technoloway.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/Technoloway.Web", "RelativePath": "Technoloway.Web#[.{fingerprint=btisj24con}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "f0ib5o3ag5", "Integrity": "72RHN3eTubIZVjEZactNL1gtgMjQIrVfDuIfhwQDTvk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\Project By AI\\Technoloway Website design by AI\\Technoloway (Processing)\\Technoloway.Web\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\Technoloway.Web.bundle.scp.css", "FileLength": 545, "LastWriteTime": "2025-06-01T14:56:49.3083013+00:00"}}, "CachedCopyCandidates": {}}