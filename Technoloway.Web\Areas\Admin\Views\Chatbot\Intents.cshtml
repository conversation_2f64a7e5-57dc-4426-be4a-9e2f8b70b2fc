@model IEnumerable<Technoloway.Core.Entities.ChatbotIntent>
@{
    ViewData["Title"] = "Chatbot Intents";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-robot mr-2"></i>
                        Chatbot Intents
                    </h3>
                    <a href="@Url.Action("CreateIntent")" class="btn btn-primary">
                        <i class="fas fa-plus mr-1"></i>
                        Add New Intent
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Name</th>
                                        <th>Display Name</th>
                                        <th>Description</th>
                                        <th>Icon</th>
                                        <th>Keywords</th>
                                        <th>Responses</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var intent in Model)
                                    {
                                        <tr>
                                            <td>@intent.DisplayOrder</td>
                                            <td>
                                                <code>@intent.Name</code>
                                            </td>
                                            <td>@intent.DisplayName</td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(intent.Description))
                                                {
                                                    <span class="text-muted">@intent.Description.Substring(0, Math.Min(50, intent.Description.Length))@(intent.Description.Length > 50 ? "..." : "")</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(intent.IconClass))
                                                {
                                                    <i class="@intent.IconClass"></i>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge badge-info">@intent.Keywords.Count</span>
                                                <a href="@Url.Action("Keywords", new { intentId = intent.Id })" class="btn btn-sm btn-outline-info ml-1">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge badge-success">@intent.Responses.Count</span>
                                                <a href="@Url.Action("Responses", new { intentId = intent.Id })" class="btn btn-sm btn-outline-success ml-1">
                                                    <i class="fas fa-comments"></i>
                                                </a>
                                            </td>
                                            <td>
                                                @if (intent.IsActive)
                                                {
                                                    <span class="badge badge-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-secondary">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditIntent", new { id = intent.Id })" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(@intent.Id, '@intent.DisplayName')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-robot fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No chatbot intents found</h5>
                            <p class="text-muted">Create your first intent to get started with the chatbot system.</p>
                            <a href="@Url.Action("CreateIntent")" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Create First Intent
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the intent "<span id="intentName"></span>"?</p>
                <p class="text-danger"><small>This will also delete all associated keywords, responses, and quick actions.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            $('#intentName').text(name);
            $('#deleteForm').attr('action', '@Url.Action("DeleteIntent")/' + id);
            $('#deleteModal').modal('show');
        }
    </script>
}
