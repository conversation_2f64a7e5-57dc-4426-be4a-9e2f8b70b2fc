@model IEnumerable<Technoloway.Core.Entities.ChatbotIntent>
@{
    ViewData["Title"] = "Chatbot Intents";
}

<div class="admin-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-brain me-2"></i>
                Chatbot Intents
            </h1>
            <p class="page-subtitle">Manage conversation intents and user interactions</p>
        </div>
        <div class="header-actions">
            <a href="@Url.Action("Index")" class="btn-modern-admin secondary">
                <i class="fas fa-arrow-left"></i>
                <span>Back to Dashboard</span>
            </a>
            <a href="@Url.Action("CreateIntent")" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                <span>Add Intent</span>
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-brain"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count()</h3>
                <p class="admin-stat-label">Total Intents</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>All conversation intents</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card success">
            <div class="admin-stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(i => i.IsActive)</h3>
                <p class="admin-stat-label">Active Intents</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>Currently enabled</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card warning">
            <div class="admin-stat-icon">
                <i class="fas fa-pause-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(i => !i.IsActive)</h3>
                <p class="admin-stat-label">Inactive Intents</p>
                <div class="admin-stat-change neutral">
                    <i class="fas fa-minus"></i>
                    <span>Currently disabled</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card info">
            <div class="admin-stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@(Model.Any() ? Model.Max(i => i.CreatedAt).ToString("MMM dd") : "N/A")</h3>
                <p class="admin-stat-label">Latest Added</p>
                <div class="admin-stat-change neutral">
                    <i class="fas fa-calendar"></i>
                    <span>Most recent intent</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="data-table-card">
        <div class="table-header">
            <div class="header-content">
                <h3 class="table-title">
                    <i class="fas fa-brain me-2"></i>
                    All Intents
                </h3>
                <p class="table-subtitle">Manage your chatbot conversation intents</p>
            </div>
            <div class="header-actions">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search intents..." id="intentSearch">
                </div>
            </div>
        </div>
        <div class="table-container">
            @if (Model.Any())
            {
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Intent</th>
                            <th>Description</th>
                            <th>Keywords</th>
                            <th>Responses</th>
                            <th>Status</th>
                            <th>Order</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var intent in Model)
                        {
                            <tr>
                                <td>
                                    <div class="table-cell-content">
                                        <div class="cell-main">
                                            @if (!string.IsNullOrEmpty(intent.IconClass))
                                            {
                                                <i class="@intent.IconClass me-2"></i>
                                            }
                                            <strong>@intent.DisplayName</strong>
                                        </div>
                                        <div class="cell-sub">
                                            <code>@intent.Name</code>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="description-cell">
                                        @if (!string.IsNullOrEmpty(intent.Description))
                                        {
                                            <span>@(intent.Description.Length > 50 ? intent.Description.Substring(0, 50) + "..." : intent.Description)</span>
                                        }
                                        else
                                        {
                                            <span class="text-muted">No description</span>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="badge badge-info">@intent.Keywords.Count</span>
                                        <a href="@Url.Action("Keywords", new { intentId = intent.Id })" class="action-link" title="Manage Keywords">
                                            <i class="fas fa-key"></i>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <span class="badge badge-success">@intent.Responses.Count</span>
                                        <a href="@Url.Action("Responses", new { intentId = intent.Id })" class="action-link" title="Manage Responses">
                                            <i class="fas fa-comments"></i>
                                        </a>
                                    </div>
                                </td>
                                <td>
                                    @if (intent.IsActive)
                                    {
                                        <span class="status-badge active">Active</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge inactive">Inactive</span>
                                    }
                                </td>
                                <td>
                                    <span class="order-badge">@intent.DisplayOrder</span>
                                </td>
                                <td>
                                    <span class="date-text">@intent.CreatedAt.ToString("MMM dd, yyyy")</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="@Url.Action("EditIntent", new { id = intent.Id })" class="action-btn edit" title="Edit Intent">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="action-btn delete" onclick="confirmDelete(@intent.Id, '@intent.DisplayName')" title="Delete Intent">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div class="empty-table">
                    <i class="fas fa-brain"></i>
                    <h3>No Intents Found</h3>
                    <p>Get started by creating your first chatbot intent to handle user conversations.</p>
                    <a href="@Url.Action("CreateIntent")" class="btn-modern-admin primary">
                        <i class="fas fa-plus"></i>
                        <span>Create Your First Intent</span>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content admin-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="delete-confirmation">
                    <div class="confirmation-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h6 class="confirmation-title">Are you sure you want to delete this intent?</h6>
                    <p class="confirmation-text">
                        You are about to delete "<span id="intentName" class="fw-bold"></span>".
                        This action cannot be undone.
                    </p>
                    <div class="confirmation-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        This will also permanently delete all associated keywords, responses, and quick actions.
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-modern-admin secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn-modern-admin danger">
                        <i class="fas fa-trash"></i>
                        <span>Delete Intent</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('intentName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("DeleteIntent", "Chatbot", new { Area = "Admin" })' + '?id=' + id;
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('intentSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('.data-table tbody tr');

                    tableRows.forEach(row => {
                        const intentName = row.querySelector('.cell-main').textContent.toLowerCase();
                        const description = row.querySelector('.description-cell').textContent.toLowerCase();

                        if (intentName.includes(searchTerm) || description.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
}
