@model Technoloway.Web.Models.BlogDetailsViewModel

@{
    ViewData["Title"] = Model.Post.MetaTitle;
    ViewData["MetaDescription"] = Model.Post.MetaDescription;
    ViewData["MetaKeywords"] = Model.Post.MetaKeywords;
}

<!-- Page Header -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="fw-bold mb-3">@Model.Post.Title</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Blog</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Article</li>
                    </ol>
                </nav>
                <div class="mt-3 d-flex flex-wrap align-items-center">
                    <span class="text-white me-3 mb-2">
                        <i class="fas fa-user me-1"></i> @Model.Post.AuthorName
                    </span>
                    <span class="text-white me-3 mb-2">
                        <i class="fas fa-calendar-alt me-1"></i> @Model.Post.PublishedAt?.ToString("MMMM dd, yyyy")
                    </span>
                    @if (!string.IsNullOrEmpty(Model.Post.Categories))
                    {
                        <div class="text-white mb-2">
                            <i class="fas fa-tags me-1"></i>
                            @foreach (var category in Model.Post.Categories.Split(','))
                            {
                                <a href="@Url.Action("Index", new { category = category.Trim() })" class="badge bg-primary text-decoration-none me-1 mb-1">@category.Trim()</a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Blog Post Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <!-- Featured Image -->
                <div class="position-relative mb-4">
                    <img src="@Model.Post.FeaturedImageUrl" alt="@Model.Post.Title" class="img-fluid rounded shadow w-100" style="max-height: 400px; object-fit: cover;" loading="lazy">
                </div>

                <!-- Post Content -->
                <div class="blog-content mb-5">
                    @Html.Raw(Model.Post.Content)
                </div>

                <!-- Share Buttons -->
                <div class="blog-share mb-5">
                    <h4 class="fw-bold mb-3">Share This Post</h4>
                    <div class="d-flex flex-wrap justify-content-center justify-content-md-start">
                        <a href="#" class="btn btn-outline-primary me-2 mb-2" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href), '_blank'); return false;" title="Share on Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary me-2 mb-2" onclick="window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(window.location.href) + '&text=' + encodeURIComponent('@Model.Post.Title'), '_blank'); return false;" title="Share on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary me-2 mb-2" onclick="window.open('https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(window.location.href), '_blank'); return false;" title="Share on LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary mb-2" onclick="navigator.clipboard.writeText(window.location.href); alert('Link copied to clipboard!'); return false;" title="Copy Link">
                            <i class="fas fa-link"></i>
                        </a>
                    </div>
                </div>

                <!-- Author Bio -->
                <div class="card border-0 shadow mb-5">
                    <div class="card-body blog-author-bio">
                        <div class="d-flex align-items-center">
                            <img src="/images/team/default-avatar.jpg" alt="@Model.Post.AuthorName" class="rounded-circle me-3" width="80" height="80">
                            <div>
                                <h4 class="fw-bold mb-1">@Model.Post.AuthorName</h4>
                                <p class="mb-0">Author at Technoloway</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comments Section -->
                <div class="blog-comments mb-5">
                    <h4 class="fw-bold mb-4">Leave a Comment</h4>
                    <form>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Name *</label>
                                <input type="text" class="form-control" id="name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="comment" class="form-label">Comment *</label>
                            <textarea class="form-control" id="comment" rows="5" required placeholder="Share your thoughts..."></textarea>
                        </div>
                        <div class="d-grid d-md-block">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Submit Comment
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Search Widget -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body">
                        <h4 class="card-title fw-bold mb-3">Search</h4>
                        <form method="get" action="@Url.Action("Index")">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search for..." name="search">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Categories Widget -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body">
                        <h4 class="card-title fw-bold mb-3">Categories</h4>
                        <div class="list-group list-group-flush">
                            <a href="@Url.Action("Index")" class="list-group-item list-group-item-action border-0">
                                All Categories
                            </a>
                            <a href="@Url.Action("Index", new { category = "Web Development" })" class="list-group-item list-group-item-action border-0">
                                Web Development
                            </a>
                            <a href="@Url.Action("Index", new { category = "Mobile Development" })" class="list-group-item list-group-item-action border-0">
                                Mobile Development
                            </a>
                            <a href="@Url.Action("Index", new { category = "UI/UX Design" })" class="list-group-item list-group-item-action border-0">
                                UI/UX Design
                            </a>
                            <a href="@Url.Action("Index", new { category = "Cloud Computing" })" class="list-group-item list-group-item-action border-0">
                                Cloud Computing
                            </a>
                            <a href="@Url.Action("Index", new { category = "DevOps" })" class="list-group-item list-group-item-action border-0">
                                DevOps
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Related Posts Widget -->
                @if (Model.RelatedPosts != null && Model.RelatedPosts.Any())
                {
                    <div class="card border-0 shadow mb-4">
                        <div class="card-body">
                            <h4 class="card-title fw-bold mb-3">Related Posts</h4>
                            <div class="list-group list-group-flush">
                                @foreach (var relatedPost in Model.RelatedPosts)
                                {
                                    <a href="@Url.Action("Details", new { slug = relatedPost.Slug })" class="list-group-item list-group-item-action border-0 d-flex align-items-center">
                                        <img src="@relatedPost.FeaturedImageUrl" alt="@relatedPost.Title" class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                                        <div>
                                            <h6 class="mb-1">@relatedPost.Title</h6>
                                            <small class="text-muted">@relatedPost.PublishedAt?.ToString("MMM dd, yyyy")</small>
                                        </div>
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                }

                <!-- Newsletter Widget -->
                <div class="card border-0 shadow">
                    <div class="card-body">
                        <h4 class="card-title fw-bold mb-3">Subscribe to Our Newsletter</h4>
                        <p>Get the latest updates and news delivered to your inbox.</p>
                        <form>
                            <div class="mb-3">
                                <input type="email" class="form-control" placeholder="Your Email Address" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- More Posts Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="fw-bold mb-4 text-center">More Articles</h2>
        <div class="text-center">
            <a asp-action="Index" class="btn btn-primary btn-lg">
                <i class="fas fa-book me-2"></i> View All Blog Posts
            </a>
        </div>
    </div>
</section>
