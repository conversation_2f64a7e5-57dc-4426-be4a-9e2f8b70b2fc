@model IEnumerable<Technoloway.Core.Entities.HeroSection>
@using Technoloway.Core.Entities

@{
    ViewData["Title"] = "Hero Sections";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-image me-2"></i>
                Hero Sections
            </h1>
            <p class="page-subtitle">Manage homepage hero sections and slideshow content</p>
        </div>
        <div class="header-actions">
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                <span>Create Hero Section</span>
            </a>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-image"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count()</h3>
                <p class="admin-stat-label">Total Hero Sections</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>All hero sections</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card success">
            <div class="admin-stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(h => h.IsActive)</h3>
                <p class="admin-stat-label">Active Sections</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>Currently enabled</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card info">
            <div class="admin-stat-icon">
                <i class="fas fa-play"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(h => h.EnableSlideshow)</h3>
                <p class="admin-stat-label">Slideshow Enabled</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>With slideshow</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card warning">
            <div class="admin-stat-icon">
                <i class="fas fa-images"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Sum(h => h.Slides.Count(s => !s.IsDeleted))</h3>
                <p class="admin-stat-label">Total Slides</p>
                <div class="admin-stat-change neutral">
                    <i class="fas fa-minus"></i>
                    <span>Across all sections</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="data-table-card">
        <div class="table-header">
            <div class="header-content">
                <h3 class="table-title">
                    <i class="fas fa-image me-2"></i>
                    All Hero Sections
                </h3>
                <p class="table-subtitle">Manage your homepage hero sections and slideshow content</p>
            </div>
            <div class="header-actions">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search hero sections..." id="heroSearch">
                </div>
            </div>
        </div>
        <div class="table-container">
            @if (Model.Any())
            {
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Hero Section</th>
                            <th>Main Content</th>
                            <th>Slides</th>
                            <th>Status</th>
                            <th>Slideshow</th>
                            <th>Last Modified</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>
                                    <div class="table-cell-content">
                                        <div class="cell-main">
                                            <strong>@item.Title</strong>
                                        </div>
                                        @if (!string.IsNullOrEmpty(item.MetaDescription))
                                        {
                                            <div class="cell-sub">@item.MetaDescription</div>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="table-cell-content">
                                        <div class="cell-main">@Html.Raw(item.MainTitle)</div>
                                        @if (!string.IsNullOrEmpty(item.MainSubtitle))
                                        {
                                            <div class="cell-sub">@(item.MainSubtitle.Length > 50 ? item.MainSubtitle.Substring(0, 50) + "..." : item.MainSubtitle)</div>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-info">@item.Slides.Count(s => !s.IsDeleted)</span>
                                </td>
                                <td>
                                    @if (item.IsActive)
                                    {
                                        <span class="status-badge active">Active</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge inactive">Inactive</span>
                                    }
                                </td>
                                <td>
                                    @if (item.EnableSlideshow)
                                    {
                                        <div class="slideshow-info">
                                            <span class="status-badge active">Enabled</span>
                                            <small class="text-muted d-block">@item.SlideshowSpeed ms</small>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="status-badge inactive">Disabled</span>
                                    }
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date-text">@item.LastModified.ToString("MMM dd, yyyy")</span>
                                        <small class="text-muted d-block">@item.LastModified.ToString("HH:mm")</small>
                                        @if (!string.IsNullOrEmpty(item.ModifiedBy))
                                        {
                                            <small class="text-muted">by @item.ModifiedBy</small>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a asp-action="Details" asp-route-id="@item.Id" class="action-btn view" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@item.Id" class="action-btn edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.Id" class="action-btn delete" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div class="empty-table">
                    <i class="fas fa-image"></i>
                    <h3>No Hero Sections Found</h3>
                    <p>Create your first hero section to manage homepage slideshow content and call-to-action elements.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus"></i>
                        <span>Create First Hero Section</span>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('heroSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('.data-table tbody tr');

                    tableRows.forEach(row => {
                        const title = row.querySelector('.cell-main').textContent.toLowerCase();
                        const description = row.querySelector('.cell-sub')?.textContent.toLowerCase() || '';

                        if (title.includes(searchTerm) || description.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
}
