@model IEnumerable<Technoloway.Core.Entities.HeroSection>
@using Technoloway.Core.Entities

@{
    ViewData["Title"] = "Hero Sections";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="admin-page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="admin-page-title">
                <i class="fas fa-image me-2"></i>
                Hero Sections
            </h1>
            <p class="admin-page-subtitle">Manage homepage hero sections and slideshow content</p>
        </div>
        <div>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create New Hero Section
            </a>
        </div>
    </div>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="admin-content-card">
    @if (Model.Any())
    {
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Title</th>
                        <th>Main Title</th>
                        <th>Slides Count</th>
                        <th>Status</th>
                        <th>Slideshow</th>
                        <th>Last Modified</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>
                                <div class="fw-semibold">@item.Title</div>
                                @if (!string.IsNullOrEmpty(item.MetaDescription))
                                {
                                    <small class="text-muted">@item.MetaDescription</small>
                                }
                            </td>
                            <td>
                                <div class="fw-medium">@Html.Raw(item.MainTitle)</div>
                                @if (!string.IsNullOrEmpty(item.MainSubtitle))
                                {
                                    <small class="text-muted">@item.MainSubtitle.Substring(0, Math.Min(50, item.MainSubtitle.Length))@(item.MainSubtitle.Length > 50 ? "..." : "")</small>
                                }
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    @item.Slides.Count(s => !s.IsDeleted) slides
                                </span>
                            </td>
                            <td>
                                @if (item.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>Active
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-pause me-1"></i>Inactive
                                    </span>
                                }
                            </td>
                            <td>
                                @if (item.EnableSlideshow)
                                {
                                    <span class="badge bg-primary">
                                        <i class="fas fa-play me-1"></i>Enabled
                                    </span>
                                    <br>
                                    <small class="text-muted">@item.SlideshowSpeed ms</small>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-stop me-1"></i>Disabled
                                    </span>
                                }
                            </td>
                            <td>
                                <div class="text-muted small">
                                    @item.LastModified.ToString("MMM dd, yyyy")
                                    <br>
                                    @item.LastModified.ToString("HH:mm")
                                </div>
                                @if (!string.IsNullOrEmpty(item.ModifiedBy))
                                {
                                    <small class="text-muted">by @item.ModifiedBy</small>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="empty-state text-center py-5">
            <div class="empty-state-icon mb-3">
                <i class="fas fa-image fa-3x text-muted"></i>
            </div>
            <h3 class="empty-state-title">No Hero Sections Found</h3>
            <p class="empty-state-description text-muted mb-4">
                Create your first hero section to manage homepage slideshow content and call-to-action elements.
            </p>
            <a asp-action="Create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Create First Hero Section
            </a>
        </div>
    }
</div>

<style>
    .admin-content-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    .table td {
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .btn-group .btn {
        margin-right: 2px;
    }

    .btn-group .btn:last-child {
        margin-right: 0;
    }

    .badge {
        font-size: 0.75rem;
    }

    .empty-state-icon {
        opacity: 0.5;
    }

    .empty-state-title {
        color: #495057;
        font-weight: 600;
    }

    .empty-state-description {
        max-width: 400px;
        margin: 0 auto;
    }
</style>
