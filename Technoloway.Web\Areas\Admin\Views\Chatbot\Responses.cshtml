@model IEnumerable<Technoloway.Core.Entities.ChatbotResponse>
@{
    ViewData["Title"] = "Chatbot Responses";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Chatbot Responses
                        @if (ViewBag.IntentName != null)
                        {
                            <small class="text-muted">for @ViewBag.IntentName</small>
                        }
                    </h2>
                    <p class="text-muted mb-0">Manage chatbot response messages and content</p>
                </div>
                <div class="d-flex gap-2">
                    @if (ViewBag.IntentId != null)
                    {
                        <a href="@Url.Action("CreateResponse", new { intentId = ViewBag.IntentId })" class="btn btn-primary btn-lg shadow-sm">
                            <i class="fas fa-plus me-2"></i>
                            Add Response
                        </a>
                        <a href="@Url.Action("Intents")" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Intents
                        </a>
                    }
                    else
                    {
                        <a href="@Url.Action("CreateResponse")" class="btn btn-primary btn-lg shadow-sm">
                            <i class="fas fa-plus me-2"></i>
                            Add Response
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    @if (ViewBag.IntentId == null)
    {
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-filter me-2 text-primary"></i>
                            Filter by Intent
                        </label>
                        <select class="form-select" onchange="filterByIntent(this.value)">
                            <option value="">All Intents</option>
                            @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                            {
                                <option value="@intent.Id">@intent.DisplayName</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-comments fa-lg text-primary"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count()</h4>
                    <p class="text-muted mb-0 small">Total Responses</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle fa-lg text-success"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count(r => r.IsActive)</h4>
                    <p class="text-muted mb-0 small">Active Responses</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-brain fa-lg text-info"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Select(r => r.ChatbotIntentId).Distinct().Count()</h4>
                    <p class="text-muted mb-0 small">Linked Intents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-mouse-pointer fa-lg text-warning"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Sum(r => r.QuickActions.Count)</h4>
                    <p class="text-muted mb-0 small">Quick Actions</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var response in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border-0 shadow-sm h-100 response-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-comments me-2 text-primary"></i>
                                        <h5 class="card-title mb-0">@response.Title</h5>
                                    </div>
                                    <p class="text-muted small mb-0">
                                        <i class="fas fa-brain me-1"></i>
                                        @response.ChatbotIntent.DisplayName
                                    </p>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="@Url.Action("EditResponse", new { id = response.Id })">
                                                <i class="fas fa-edit me-2"></i>Edit Response
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger" onclick="confirmDelete(@response.Id, '@response.Title')">
                                                <i class="fas fa-trash me-2"></i>Delete Response
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="content-preview bg-light p-3 rounded">
                                    @if (response.ResponseType == "html")
                                    {
                                        <div class="small text-muted">
                                            @Html.Raw(response.Content.Length > 150 ? response.Content.Substring(0, 150) + "..." : response.Content)
                                        </div>
                                    }
                                    else
                                    {
                                        <p class="small text-muted mb-0">
                                            @(response.Content.Length > 150 ? response.Content.Substring(0, 150) + "..." : response.Content)
                                        </p>
                                    }
                                </div>
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-code text-info me-2"></i>
                                        <span class="small text-muted">Type:</span>
                                        <span class="badge bg-info ms-2">@response.ResponseType</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-mouse-pointer text-warning me-2"></i>
                                        <span class="small text-muted">Actions:</span>
                                        <span class="badge bg-warning ms-2">@response.QuickActions.Count</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if (response.IsActive)
                                    {
                                        <span class="badge bg-success-subtle text-success border border-success-subtle">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle">
                                            <i class="fas fa-pause-circle me-1"></i>Inactive
                                        </span>
                                    }
                                </div>
                                <small class="text-muted">
                                    Order: @response.DisplayOrder
                                </small>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="@Url.Action("EditResponse", new { id = response.Id })"
                                   class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="empty-state">
                            <div class="mb-4">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-4 d-inline-flex">
                                    <i class="fas fa-comments fa-3x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="mb-3">No Responses Found</h4>
                            @if (ViewBag.IntentId != null)
                            {
                                <p class="text-muted mb-4">Create your first response for this intent to start conversations.</p>
                                <a href="@Url.Action("CreateResponse", new { intentId = ViewBag.IntentId })" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>
                                    Create Your First Response
                                </a>
                            }
                            else
                            {
                                <p class="text-muted mb-4">Create your first chatbot response to get started.</p>
                                <a href="@Url.Action("CreateResponse")" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>
                                    Create Your First Response
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure?
                    </h6>
                    <p class="mb-2">You are about to delete the response "<span id="responseName" class="fw-bold"></span>".</p>
                    <hr>
                    <p class="mb-0 small">
                        <i class="fas fa-info-circle me-1"></i>
                        This will also permanently delete all associated quick actions.
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Response
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            $('#responseName').text(name);
            $('#deleteForm').attr('action', '@Url.Action("DeleteResponse", "Chatbot", new { Area = "Admin" })' + '?id=' + id);
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        function filterByIntent(intentId) {
            if (intentId) {
                window.location.href = '@Url.Action("Responses")?intentId=' + intentId;
            } else {
                window.location.href = '@Url.Action("Responses")';
            }
        }
    </script>
}
