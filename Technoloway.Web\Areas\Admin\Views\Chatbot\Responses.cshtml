@model IEnumerable<Technoloway.Core.Entities.ChatbotResponse>
@{
    ViewData["Title"] = "Chatbot Responses";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-comments mr-2"></i>
                        Chatbot Responses
                        @if (ViewBag.IntentName != null)
                        {
                            <small class="text-muted">for @ViewBag.IntentName</small>
                        }
                    </h3>
                    <div class="btn-group">
                        @if (ViewBag.IntentId != null)
                        {
                            <a href="@Url.Action("CreateResponse", new { intentId = ViewBag.IntentId })" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Add Response
                            </a>
                            <a href="@Url.Action("Intents")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i>
                                Back to Intents
                            </a>
                        }
                        else
                        {
                            <a href="@Url.Action("CreateResponse")" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Add Response
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    @if (ViewBag.IntentId == null)
                    {
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Filter by Intent:</label>
                                <select class="form-control" onchange="filterByIntent(this.value)">
                                    <option value="">All Intents</option>
                                    @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                                    {
                                        <option value="@intent.Id">@intent.DisplayName</option>
                                    }
                                </select>
                            </div>
                        </div>
                    }

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Order</th>
                                        <th>Title</th>
                                        <th>Intent</th>
                                        <th>Content Preview</th>
                                        <th>Type</th>
                                        <th>Quick Actions</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var response in Model)
                                    {
                                        <tr>
                                            <td>@response.DisplayOrder</td>
                                            <td>@response.Title</td>
                                            <td>
                                                <span class="badge badge-info">@response.ChatbotIntent.DisplayName</span>
                                            </td>
                                            <td>
                                                <div class="content-preview">
                                                    @if (response.ResponseType == "html")
                                                    {
                                                        @Html.Raw(response.Content.Length > 100 ? response.Content.Substring(0, 100) + "..." : response.Content)
                                                    }
                                                    else
                                                    {
                                                        @(response.Content.Length > 100 ? response.Content.Substring(0, 100) + "..." : response.Content)
                                                    }
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">@response.ResponseType</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-success">@response.QuickActions.Count</span>
                                            </td>
                                            <td>
                                                @if (response.IsActive)
                                                {
                                                    <span class="badge badge-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-secondary">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditResponse", new { id = response.Id })" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(@response.Id, '@response.Title')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-comments fa-3x text-secondary mb-3"></i>
                            <h5 class="text-dark">No responses found</h5>
                            @if (ViewBag.IntentId != null)
                            {
                                <p class="text-secondary">Create your first response for this intent.</p>
                                <a href="@Url.Action("CreateResponse", new { intentId = ViewBag.IntentId })" class="btn btn-primary">
                                    <i class="fas fa-plus mr-1"></i>
                                    Create First Response
                                </a>
                            }
                            else
                            {
                                <p class="text-secondary">Create your first chatbot response.</p>
                                <a href="@Url.Action("CreateResponse")" class="btn btn-primary">
                                    <i class="fas fa-plus mr-1"></i>
                                    Create First Response
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="text-dark">Are you sure you want to delete the response "<span id="responseName" class="font-weight-bold"></span>"?</p>
                <p class="text-danger"><small>This will also delete all associated quick actions.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            $('#responseName').text(name);
            $('#deleteForm').attr('action', '@Url.Action("DeleteResponse", "Chatbot", new { Area = "Admin" })' + '?id=' + id);
            $('#deleteModal').modal('show');
        }

        function filterByIntent(intentId) {
            if (intentId) {
                window.location.href = '@Url.Action("Responses")?intentId=' + intentId;
            } else {
                window.location.href = '@Url.Action("Responses")';
            }
        }
    </script>
    <style>
        .content-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
}
