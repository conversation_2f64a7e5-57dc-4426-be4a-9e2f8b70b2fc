# Technoloway - Enterprise Web Application

A comprehensive ASP.NET Core web application for technology consulting services with client portal, admin dashboard, and secure file management.

## 🚀 Features

### Public Website
- Modern responsive homepage with hero sections
- Services showcase with detailed descriptions
- Project portfolio with technology filtering
- Team member profiles
- Blog system with rich content
- Contact forms with validation
- Legal pages (Privacy Policy, Terms of Service)

### Client Portal
- Secure client authentication
- Project management dashboard
- Document sharing and downloads
- Invoice viewing and payment processing
- Messaging system with project teams
- Feedback submission system

### Admin Dashboard
- User and role management
- Content management (services, projects, blog posts)
- Client and invoice management
- Site settings configuration
- File upload management
- Analytics and reporting

## 🛡️ Security Features

- **Authentication & Authorization**: ASP.NET Core Identity with role-based access
- **Account Security**: Account lockout, password complexity, session management
- **File Upload Security**: MIME type validation, file signature checking, virus scanning
- **Input Validation**: Comprehensive validation with anti-forgery tokens
- **Configuration Security**: Environment-based configuration with secrets management
- **HTTPS Enforcement**: HSTS headers and secure cookie policies

## 🏗️ Architecture

### Clean Architecture Pattern
```
├── Technoloway.Web/          # Presentation Layer
├── Technoloway.Core/         # Domain Layer
└── Technoloway.Infrastructure/ # Data Access Layer
```

### Technology Stack
- **Framework**: ASP.NET Core 9.0
- **Database**: SQLite (development) / SQL Server (production)
- **ORM**: Entity Framework Core
- **Authentication**: ASP.NET Core Identity
- **Frontend**: Bootstrap 5, jQuery, Font Awesome
- **Payment**: Stripe Integration
- **Maps**: Google Maps API

## 📋 Prerequisites

- .NET 9.0 SDK
- Visual Studio 2022 or VS Code
- SQL Server (for production)
- Node.js (for frontend build tools)

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone https://github.com/your-org/technoloway.git
cd technoloway
```

### 2. Configure Settings
```bash
# Copy and configure settings
cp Technoloway.Web/appsettings.json Technoloway.Web/appsettings.Development.json

# Set up user secrets for development
dotnet user-secrets init --project Technoloway.Web
dotnet user-secrets set "Stripe:SecretKey" "sk_test_your_key" --project Technoloway.Web
dotnet user-secrets set "GoogleMaps:ApiKey" "your_api_key" --project Technoloway.Web
```

### 3. Database Setup
```bash
# Install EF Core tools
dotnet tool install --global dotnet-ef

# Create and seed database
dotnet ef database update --project Technoloway.Web
```

### 4. Run the Application
```bash
# Using the batch file (Windows)
run-app.bat

# Or using dotnet CLI
dotnet run --project Technoloway.Web --urls "http://localhost:5258"
```

### 5. Default Accounts
- **Admin**: <EMAIL> / Admin123!
- **Demo Client**: <EMAIL> / Client123!

## ⚙️ Configuration

### Environment Variables (Production)
```bash
ConnectionStrings__DefaultConnection="Server=...;Database=...;Trusted_Connection=true;"
Stripe__SecretKey="sk_live_..."
Stripe__PublishableKey="pk_live_..."
GoogleMaps__ApiKey="your_production_api_key"
```

### Security Settings
```json
{
  "Security": {
    "RequireEmailConfirmation": true,
    "EnableAccountLockout": true,
    "MaxFailedAccessAttempts": 5,
    "LockoutTimeSpan": "00:15:00",
    "PasswordRequiredLength": 12,
    "SessionTimeoutMinutes": 20
  }
}
```

### File Upload Settings
```json
{
  "FileUpload": {
    "MaxFileSizeBytes": ********,
    "AllowedImageExtensions": [".jpg", ".jpeg", ".png", ".webp"],
    "AllowedDocumentExtensions": [".pdf"],
    "ScanForViruses": true,
    "EnableFileSignatureValidation": true
  }
}
```

## 🧪 Testing

### Run Unit Tests
```bash
dotnet test
```

### Run Integration Tests
```bash
dotnet test --filter Category=Integration
```

## 📦 Deployment

### Docker Deployment
```bash
# Build image
docker build -t technoloway .

# Run container
docker run -p 80:80 -e ASPNETCORE_ENVIRONMENT=Production technoloway
```

### Azure Deployment
```bash
# Publish to Azure App Service
dotnet publish -c Release
az webapp deploy --resource-group myResourceGroup --name myAppName --src-path ./bin/Release/net9.0/publish
```

## 🔧 Development

### Project Structure
```
Technoloway.Web/
├── Areas/
│   ├── Admin/          # Admin dashboard
│   ├── Client/         # Client portal
│   └── Identity/       # Authentication pages
├── Controllers/        # Public controllers
├── Services/          # Application services
├── Views/             # Razor views
└── wwwroot/           # Static files

Technoloway.Core/
├── Entities/          # Domain entities
├── Interfaces/        # Repository interfaces
├── Configuration/     # Settings models
└── Common/            # Shared components

Technoloway.Infrastructure/
├── Data/              # DbContext and migrations
├── Repositories/      # Data access implementations
└── Configuration/     # Configuration validators
```

### Adding New Features
1. Create entity in `Technoloway.Core/Entities/`
2. Add repository interface in `Technoloway.Core/Interfaces/`
3. Implement repository in `Technoloway.Infrastructure/Repositories/`
4. Register in `DependencyInjection.cs`
5. Create controller and views

### Database Migrations
```bash
# Add new migration
dotnet ef migrations add MigrationName --project Technoloway.Infrastructure --startup-project Technoloway.Web

# Update database
dotnet ef database update --project Technoloway.Web
```

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**
- Verify connection string in appsettings.json
- Ensure SQL Server is running
- Check firewall settings

**File Upload Issues**
- Verify upload directory permissions
- Check file size limits
- Ensure allowed file types are configured

**Authentication Issues**
- Clear browser cookies
- Check Identity configuration
- Verify user roles in database

## 📚 API Documentation

API documentation is available at `/swagger` when running in development mode.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Wiki](https://github.com/your-org/technoloway/wiki)
