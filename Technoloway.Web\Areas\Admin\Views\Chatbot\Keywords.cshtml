@model IEnumerable<Technoloway.Core.Entities.ChatbotKeyword>
@{
    ViewData["Title"] = "Chatbot Keywords";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-key me-2 text-primary"></i>
                        Chatbot Keywords
                        @if (ViewBag.IntentName != null)
                        {
                            <small class="text-muted">for @ViewBag.IntentName</small>
                        }
                    </h2>
                    <p class="text-muted mb-0">Manage keywords that trigger chatbot responses</p>
                </div>
                <div class="d-flex gap-2">
                    @if (ViewBag.IntentId != null)
                    {
                        <a href="@Url.Action("CreateKeyword", new { intentId = ViewBag.IntentId })" class="btn btn-primary btn-lg shadow-sm">
                            <i class="fas fa-plus me-2"></i>
                            Add Keyword
                        </a>
                        <a href="@Url.Action("Intents")" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Intents
                        </a>
                    }
                    else
                    {
                        <a href="@Url.Action("CreateKeyword")" class="btn btn-primary btn-lg shadow-sm">
                            <i class="fas fa-plus me-2"></i>
                            Add Keyword
                        </a>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    @if (ViewBag.IntentId == null)
    {
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <label class="form-label fw-semibold">
                            <i class="fas fa-filter me-2 text-primary"></i>
                            Filter by Intent
                        </label>
                        <select class="form-select" onchange="filterByIntent(this.value)">
                            <option value="">All Intents</option>
                            @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                            {
                                <option value="@intent.Id">@intent.DisplayName</option>
                            }
                        </select>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-key fa-lg text-primary"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count()</h4>
                    <p class="text-muted mb-0 small">Total Keywords</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle fa-lg text-success"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count(k => k.IsActive)</h4>
                    <p class="text-muted mb-0 small">Active Keywords</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-brain fa-lg text-info"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Select(k => k.ChatbotIntentId).Distinct().Count()</h4>
                    <p class="text-muted mb-0 small">Linked Intents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-weight-hanging fa-lg text-warning"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@(Model.Any() ? Model.Average(k => k.Weight).ToString("F1") : "0")</h4>
                    <p class="text-muted mb-0 small">Avg Weight</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var keyword in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border-0 shadow-sm h-100 keyword-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-key me-2 text-primary"></i>
                                        <h5 class="card-title mb-0">
                                            <code class="bg-light px-2 py-1 rounded">@keyword.Keyword</code>
                                        </h5>
                                    </div>
                                    <p class="text-muted small mb-0">
                                        <i class="fas fa-brain me-1"></i>
                                        @keyword.ChatbotIntent.DisplayName
                                    </p>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="@Url.Action("EditKeyword", new { id = keyword.Id })">
                                                <i class="fas fa-edit me-2"></i>Edit Keyword
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <button class="dropdown-item text-danger" onclick="confirmDelete(@keyword.Id, '@keyword.Keyword')">
                                                <i class="fas fa-trash me-2"></i>Delete Keyword
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                @if (!string.IsNullOrEmpty(keyword.Synonyms))
                                {
                                    <div class="mb-2">
                                        <small class="text-muted d-block">Synonyms:</small>
                                        <p class="card-text small">@keyword.Synonyms</p>
                                    </div>
                                }
                                else
                                {
                                    <p class="card-text text-muted small fst-italic">No synonyms defined</p>
                                }
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-weight-hanging text-warning me-2"></i>
                                        <span class="small text-muted">Weight:</span>
                                        <span class="badge bg-warning ms-2">@keyword.Weight</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-search text-info me-2"></i>
                                        <span class="small text-muted">Match:</span>
                                        <span class="badge bg-info ms-2">@keyword.MatchType</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex gap-2">
                                    @if (keyword.IsActive)
                                    {
                                        <span class="badge bg-success-subtle text-success border border-success-subtle">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle">
                                            <i class="fas fa-pause-circle me-1"></i>Inactive
                                        </span>
                                    }
                                    @if (keyword.IsCaseSensitive)
                                    {
                                        <span class="badge bg-warning-subtle text-warning border border-warning-subtle">
                                            <i class="fas fa-font me-1"></i>Case Sensitive
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="@Url.Action("EditKeyword", new { id = keyword.Id })"
                                   class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="empty-state">
                            <div class="mb-4">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-4 d-inline-flex">
                                    <i class="fas fa-key fa-3x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="mb-3">No Keywords Found</h4>
                            @if (ViewBag.IntentId != null)
                            {
                                <p class="text-muted mb-4">Add keywords to help the chatbot understand when to use this intent.</p>
                                <a href="@Url.Action("CreateKeyword", new { intentId = ViewBag.IntentId })" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>
                                    Add Your First Keyword
                                </a>
                            }
                            else
                            {
                                <p class="text-muted mb-4">Create your first chatbot keyword to get started.</p>
                                <a href="@Url.Action("CreateKeyword")" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>
                                    Create Your First Keyword
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Are you sure?
                    </h6>
                    <p class="mb-0">You are about to delete the keyword "<span id="keywordName" class="fw-bold"></span>".</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>Delete Keyword
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            $('#keywordName').text(name);
            $('#deleteForm').attr('action', '@Url.Action("DeleteKeyword", "Chatbot", new { Area = "Admin" })' + '?id=' + id);
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        function filterByIntent(intentId) {
            if (intentId) {
                window.location.href = '@Url.Action("Keywords")?intentId=' + intentId;
            } else {
                window.location.href = '@Url.Action("Keywords")';
            }
        }
    </script>
}
