@model IEnumerable<Technoloway.Core.Entities.ChatbotKeyword>
@{
    ViewData["Title"] = "Chatbot Keywords";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-key mr-2"></i>
                        Chatbot Keywords
                        @if (ViewBag.IntentName != null)
                        {
                            <small class="text-muted">for @ViewBag.IntentName</small>
                        }
                    </h3>
                    <div class="btn-group">
                        @if (ViewBag.IntentId != null)
                        {
                            <a href="@Url.Action("CreateKeyword", new { intentId = ViewBag.IntentId })" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Add Keyword
                            </a>
                            <a href="@Url.Action("Intents")" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i>
                                Back to Intents
                            </a>
                        }
                        else
                        {
                            <a href="@Url.Action("CreateKeyword")" class="btn btn-primary">
                                <i class="fas fa-plus mr-1"></i>
                                Add Keyword
                            </a>
                        }
                    </div>
                </div>
                <div class="card-body">
                    @if (ViewBag.IntentId == null)
                    {
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Filter by Intent:</label>
                                <select class="form-control" onchange="filterByIntent(this.value)">
                                    <option value="">All Intents</option>
                                    @foreach (var intent in ViewBag.Intents as IEnumerable<Technoloway.Core.Entities.ChatbotIntent>)
                                    {
                                        <option value="@intent.Id">@intent.DisplayName</option>
                                    }
                                </select>
                            </div>
                        </div>
                    }

                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Weight</th>
                                        <th>Keyword</th>
                                        <th>Synonyms</th>
                                        <th>Intent</th>
                                        <th>Match Type</th>
                                        <th>Case Sensitive</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var keyword in Model)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge badge-primary">@keyword.Weight</span>
                                            </td>
                                            <td>
                                                <code>@keyword.Keyword</code>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(keyword.Synonyms))
                                                {
                                                    <small class="text-secondary">@keyword.Synonyms</small>
                                                }
                                                else
                                                {
                                                    <span class="text-secondary">-</span>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge badge-info">@keyword.ChatbotIntent.DisplayName</span>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">@keyword.MatchType</span>
                                            </td>
                                            <td>
                                                @if (keyword.IsCaseSensitive)
                                                {
                                                    <span class="badge badge-warning">Yes</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-light">No</span>
                                                }
                                            </td>
                                            <td>
                                                @if (keyword.IsActive)
                                                {
                                                    <span class="badge badge-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-secondary">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("EditKeyword", new { id = keyword.Id })" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete(@keyword.Id, '@keyword.Keyword')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-key fa-3x text-secondary mb-3"></i>
                            <h5 class="text-dark">No keywords found</h5>
                            @if (ViewBag.IntentId != null)
                            {
                                <p class="text-secondary">Add keywords to help the chatbot understand when to use this intent.</p>
                                <a href="@Url.Action("CreateKeyword", new { intentId = ViewBag.IntentId })" class="btn btn-primary">
                                    <i class="fas fa-plus mr-1"></i>
                                    Add First Keyword
                                </a>
                            }
                            else
                            {
                                <p class="text-secondary">Create your first chatbot keyword.</p>
                                <a href="@Url.Action("CreateKeyword")" class="btn btn-primary">
                                    <i class="fas fa-plus mr-1"></i>
                                    Add First Keyword
                                </a>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="text-dark">Are you sure you want to delete the keyword "<span id="keywordName" class="font-weight-bold"></span>"?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            $('#keywordName').text(name);
            $('#deleteForm').attr('action', '@Url.Action("DeleteKeyword")/' + id);
            $('#deleteModal').modal('show');
        }

        function filterByIntent(intentId) {
            if (intentId) {
                window.location.href = '@Url.Action("Keywords")?intentId=' + intentId;
            } else {
                window.location.href = '@Url.Action("Keywords")';
            }
        }
    </script>
}
