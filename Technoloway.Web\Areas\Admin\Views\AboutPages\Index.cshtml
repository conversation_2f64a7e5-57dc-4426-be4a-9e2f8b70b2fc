@model IEnumerable<Technoloway.Core.Entities.AboutPage>

@{
    ViewData["Title"] = "About Pages";
    ViewData["PageTitle"] = "About Pages";
    ViewData["PageDescription"] = "Manage your website's about page content";
}

<div class="admin-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-info-circle me-2"></i>
                About Pages
            </h1>
            <p class="page-subtitle">Manage your website's about page content and sections</p>
        </div>
        <div class="header-actions">
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                <span>Create About Page</span>
            </a>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="admin-stat-card">
            <div class="admin-stat-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count()</h3>
                <p class="admin-stat-label">Total About Pages</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>All about pages</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card success">
            <div class="admin-stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(a => a.IsActive)</h3>
                <p class="admin-stat-label">Active Pages</p>
                <div class="admin-stat-change positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>Currently published</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card warning">
            <div class="admin-stat-icon">
                <i class="fas fa-pause-circle"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@Model.Count(a => !a.IsActive)</h3>
                <p class="admin-stat-label">Inactive Pages</p>
                <div class="admin-stat-change neutral">
                    <i class="fas fa-minus"></i>
                    <span>Currently unpublished</span>
                </div>
            </div>
        </div>

        <div class="admin-stat-card info">
            <div class="admin-stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="admin-stat-content">
                <h3 class="admin-stat-value">@(Model.Any() ? Model.Max(a => a.LastModified).ToString("MMM dd") : "N/A")</h3>
                <p class="admin-stat-label">Last Updated</p>
                <div class="admin-stat-change neutral">
                    <i class="fas fa-calendar"></i>
                    <span>Most recent update</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="data-table-card">
        <div class="table-header">
            <div class="header-content">
                <h3 class="table-title">
                    <i class="fas fa-info-circle me-2"></i>
                    All About Pages
                </h3>
                <p class="table-subtitle">Manage your website's about page content</p>
            </div>
            <div class="header-actions">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Search about pages..." id="aboutSearch">
                </div>
            </div>
        </div>
        <div class="table-container">
            @if (Model.Any())
            {
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>About Page</th>
                            <th>Status</th>
                            <th>Last Modified</th>
                            <th>Modified By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var aboutPage in Model)
                        {
                            <tr>
                                <td>
                                    <div class="table-cell-content">
                                        <div class="cell-main">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <strong>@aboutPage.Title</strong>
                                        </div>
                                        @if (!string.IsNullOrEmpty(aboutPage.MetaDescription))
                                        {
                                            <div class="cell-sub">@aboutPage.MetaDescription</div>
                                        }
                                    </div>
                                </td>
                                <td>
                                    @if (aboutPage.IsActive)
                                    {
                                        <span class="status-badge active">Active</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge inactive">Inactive</span>
                                    }
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date-text">@aboutPage.LastModified.ToString("MMM dd, yyyy")</span>
                                        <small class="text-muted d-block">@aboutPage.LastModified.ToString("hh:mm tt")</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-secondary">
                                        @(aboutPage.ModifiedBy ?? "System")
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a asp-action="Details" asp-route-id="@aboutPage.Id" class="action-btn view" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a asp-action="Edit" asp-route-id="@aboutPage.Id" class="action-btn edit" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="action-btn delete" onclick="confirmDelete(@aboutPage.Id, '@aboutPage.Title')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div class="empty-table">
                    <i class="fas fa-info-circle"></i>
                    <h3>No About Pages Found</h3>
                    <p>Get started by creating your first about page to manage your website's about content.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus"></i>
                        <span>Create Your First About Page</span>
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content admin-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                    Confirm Delete
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="delete-confirmation">
                    <div class="confirmation-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h6 class="confirmation-title">Are you sure you want to delete this about page?</h6>
                    <p class="confirmation-text">
                        You are about to delete "<span id="deleteItemName" class="fw-bold"></span>".
                        This action cannot be undone.
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-modern-admin secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn-modern-admin danger">
                        <i class="fas fa-trash"></i>
                        <span>Delete Page</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('deleteItemName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('aboutSearch');
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const tableRows = document.querySelectorAll('.data-table tbody tr');

                    tableRows.forEach(row => {
                        const title = row.querySelector('.cell-main').textContent.toLowerCase();
                        const description = row.querySelector('.cell-sub')?.textContent.toLowerCase() || '';

                        if (title.includes(searchTerm) || description.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                });
            }
        });
    </script>
}
