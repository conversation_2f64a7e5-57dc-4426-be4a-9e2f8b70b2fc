using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Project : BaseEntity
{
    [Required(ErrorMessage = "Project name is required")]
    [StringLength(200, ErrorMessage = "Project name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [Required(ErrorMessage = "Description is required")]
    [StringLength(2000, ErrorMessage = "Description cannot exceed 2000 characters")]
    public string Description { get; set; } = string.Empty;

    public string ClientName { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Image URL cannot exceed 500 characters")]
    public string ImageUrl { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Project URL cannot exceed 500 characters")]
    public string ProjectUrl { get; set; } = string.Empty;

    [Required(ErrorMessage = "Completion date is required")]
    public DateTime CompletionDate { get; set; }

    public bool IsFeatured { get; set; } = false;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    // Navigation properties
    public int? ClientId { get; set; }
    public Client? Client { get; set; }

    [Required(ErrorMessage = "Service is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Please select a valid service")]
    public int ServiceId { get; set; }
    public Service? Service { get; set; }

    public ICollection<Technology> Technologies { get; set; } = new List<Technology>();
    public ICollection<ProjectDocument> Documents { get; set; } = new List<ProjectDocument>();
    public ICollection<Message> Messages { get; set; } = new List<Message>();
    public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
