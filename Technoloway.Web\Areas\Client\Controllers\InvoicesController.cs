using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class InvoicesController : Controller
{
    private readonly IRepository<Invoice> _invoiceRepository;
    private readonly IRepository<Payment> _paymentRepository;
    private readonly IRepository<InvoiceItem> _invoiceItemRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IRepository<Core.Entities.Client> _clientRepository;

    public InvoicesController(
        IRepository<Invoice> invoiceRepository,
        IRepository<Payment> paymentRepository,
        IRepository<InvoiceItem> invoiceItemRepository,
        UserManager<IdentityUser> userManager,
        IRepository<Core.Entities.Client> clientRepository)
    {
        _invoiceRepository = invoiceRepository;
        _paymentRepository = paymentRepository;
        _invoiceItemRepository = invoiceItemRepository;
        _userManager = userManager;
        _clientRepository = clientRepository;
    }

    public async Task<IActionResult> Index(string? status = null)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var query = _invoiceRepository.GetAll()
            .Where(i => i.ClientId == client.Id && !i.IsDeleted);

        if (!string.IsNullOrEmpty(status))
        {
            query = query.Where(i => i.Status.ToLower() == status.ToLower());
        }

        var invoices = await query
            .OrderByDescending(i => i.IssueDate)
            .ToListAsync();

        ViewBag.SelectedStatus = status;
        ViewBag.TotalInvoices = invoices.Count;
        ViewBag.PendingInvoices = invoices.Count(i => i.Status == "Pending");
        ViewBag.PaidInvoices = invoices.Count(i => i.Status == "Paid");
        ViewBag.OverdueInvoices = invoices.Count(i => i.Status == "Overdue");
        ViewBag.TotalAmount = invoices.Sum(i => i.TotalAmount);
        ViewBag.PaidAmount = invoices.Where(i => i.Status == "Paid").Sum(i => i.TotalAmount);
        ViewBag.PendingAmount = invoices.Where(i => i.Status == "Pending").Sum(i => i.TotalAmount);

        return View(invoices);
    }

    public async Task<IActionResult> Details(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var invoice = await _invoiceRepository.GetAll()
            .Where(i => i.Id == id && i.ClientId == client.Id && !i.IsDeleted)
            .Include(i => i.Project)
            .Include(i => i.Client)
            .FirstOrDefaultAsync();

        if (invoice == null)
        {
            return NotFound();
        }

        var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == id);
        var payments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);

        ViewBag.Items = items.OrderBy(i => i.CreatedAt).ToList();
        ViewBag.Payments = payments.OrderByDescending(p => p.PaymentDate).ToList();
        ViewBag.TotalPaid = payments.Sum(p => p.Amount);
        ViewBag.Balance = invoice.TotalAmount - payments.Sum(p => p.Amount);

        return View(invoice);
    }

    public async Task<IActionResult> Download(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var invoice = await _invoiceRepository.GetAll()
            .Where(i => i.Id == id && i.ClientId == client.Id && !i.IsDeleted)
            .Include(i => i.Project)
            .Include(i => i.Client)
            .FirstOrDefaultAsync();

        if (invoice == null)
        {
            return NotFound();
        }

        var items = await _invoiceItemRepository.ListAsync(i => i.InvoiceId == id);
        ViewBag.Items = items.OrderBy(i => i.CreatedAt).ToList();

        // Return a view that can be used for PDF generation
        return View("InvoicePdf", invoice);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> PayInvoice(int id, decimal amount, string paymentMethod)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "User not authenticated" });
            }

            var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
            if (client == null)
            {
                return Json(new { success = false, message = "Client profile not found" });
            }

            var invoice = await _invoiceRepository.GetAll()
                .Where(i => i.Id == id && i.ClientId == client.Id && !i.IsDeleted)
                .FirstOrDefaultAsync();

            if (invoice == null)
            {
                return Json(new { success = false, message = "Invoice not found" });
            }

            // Validation
            if (amount <= 0)
            {
                return Json(new { success = false, message = "Payment amount must be greater than 0" });
            }

            if (string.IsNullOrWhiteSpace(paymentMethod))
            {
                return Json(new { success = false, message = "Payment method is required" });
            }

            var existingPayments = await _paymentRepository.ListAsync(p => p.InvoiceId == id);
            var totalPaid = existingPayments.Sum(p => p.Amount);
            var remainingBalance = invoice.TotalAmount - totalPaid;

            if (amount > remainingBalance + 0.01m) // Small tolerance for decimal precision
            {
                return Json(new { success = false, message = $"Payment amount cannot exceed the remaining balance of {remainingBalance:C}" });
            }

            // Generate transaction ID
            var transactionId = $"TXN-{DateTime.UtcNow:yyyyMMddHHmmss}-{id}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}";

            // Create payment record
            var payment = new Payment
            {
                InvoiceId = id,
                Amount = amount,
                PaymentMethod = paymentMethod,
                PaymentDate = DateTime.UtcNow,
                Status = "Pending", // Would be updated by payment processor webhook
                TransactionId = transactionId,
                Notes = $"Payment initiated by client via portal on {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save payment
            var savedPayment = await _paymentRepository.AddAsync(payment);

            // Update invoice status if fully paid
            var newTotalPaid = totalPaid + amount;
            if (newTotalPaid >= invoice.TotalAmount - 0.01m) // Small tolerance for decimal precision
            {
                invoice.Status = "Paid";
                invoice.UpdatedAt = DateTime.UtcNow;
                await _invoiceRepository.UpdateAsync(invoice);
            }

            // Return success response for AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new
                {
                    success = true,
                    message = "Payment has been initiated successfully.",
                    paymentId = savedPayment.Id,
                    transactionId = transactionId,
                    newBalance = invoice.TotalAmount - newTotalPaid,
                    invoiceStatus = invoice.Status
                });
            }

            TempData["Success"] = "Payment has been initiated successfully.";
            return RedirectToAction("Details", new { id });
        }
        catch (Exception ex)
        {
            // Log the error (in a real application, use proper logging)
            Console.WriteLine($"Error processing payment: {ex.Message}");

            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = false, message = "An error occurred while processing the payment. Please try again." });
            }

            TempData["Error"] = "An error occurred while processing the payment. Please try again.";
            return RedirectToAction("Details", new { id });
        }
    }

    public async Task<IActionResult> Payments()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        // Get all payments for this client's invoices
        var clientInvoices = await _invoiceRepository.ListAsync(i => i.ClientId == client.Id && !i.IsDeleted);
        var invoiceIds = clientInvoices.Select(i => i.Id).ToList();

        var payments = await _paymentRepository.GetAll()
            .Where(p => invoiceIds.Contains(p.InvoiceId))
            .Include(p => p.Invoice)
            .OrderByDescending(p => p.PaymentDate)
            .ToListAsync();

        ViewBag.TotalPayments = payments.Count;
        ViewBag.TotalAmount = payments.Sum(p => p.Amount);
        ViewBag.PendingPayments = payments.Count(p => p.Status == "Pending");
        ViewBag.CompletedPayments = payments.Count(p => p.Status == "Completed");
        ViewBag.FailedPayments = payments.Count(p => p.Status == "Failed");

        return View(payments);
    }

    [HttpGet]
    public async Task<IActionResult> GetUnreadCount()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { count = 0 });
            }

            var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
            if (client == null)
            {
                return Json(new { count = 0 });
            }

            // This would typically check for unread messages or notifications
            // For now, return 0 as this is just for the auto-refresh functionality
            return Json(new { count = 0 });
        }
        catch
        {
            return Json(new { count = 0 });
        }
    }
}
